# Parquet Writing Patterns Audit

## Overview
This document audits all parquet writing patterns across the user_scripts_restructured/ codebase to identify code duplication and opportunities for centralization.

## Current Parquet Writing Patterns

### 1. process_data.py - save_data_file()
**Location**: `user_scripts_restructured/scripts/process_data.py:778-797`
**Pattern**: Direct PyArrow table writing
```python
def save_data_file(data: list, file_path: str):
    import pyarrow as pa
    import pyarrow.parquet as pq
    
    table = pa.Table.from_pylist(data)
    pq.write_table(table, file_path)
```
**Issues**:
- No metadata handling
- No Nautilus compatibility
- Basic error handling only
- No schema validation

### 2. consolidator.py - Consolidation Writing
**Location**: `user_scripts_restructured/processors/consolidator.py:207-213`
**Pattern**: PyArrow with compression settings
```python
pq.write_table(
    processed_table,
    output_file,
    compression='snappy',
    use_dictionary=True,
    row_group_size=100000
)
```
**Issues**:
- No Nautilus metadata
- Fixed compression settings
- No schema compatibility checks

### 3. continuous_futures.py - Metadata Addition
**Location**: `user_scripts_restructured/processors/continuous_futures.py:604-618`
**Pattern**: Manual Nautilus metadata addition
```python
nautilus_metadata = {
    'bar_type': f"{instrument_id}-1-MINUTE-LAST-EXTERNAL",
    'instrument_id': instrument_id,
    'price_precision': '2',
    'size_precision': '0'
}
final_table = final_table.replace_schema_metadata(nautilus_metadata)
pq.write_table(final_table, output_file)
```
**Issues**:
- Hardcoded precision values
- Manual metadata construction
- Duplicated across files

### 4. base_downloader.py - Fallback Method
**Location**: `user_scripts_restructured/downloaders/base_downloader.py:318-333`
**Pattern**: Pandas to_parquet
```python
df = pd.DataFrame(bars_data)
df.to_parquet(output_file, engine='pyarrow')
```
**Issues**:
- Uses pandas (less efficient)
- No metadata
- No Nautilus compatibility
- Timestamp-based filenames

### 5. nautilus_data_saver.py - Proper Implementation
**Location**: `user_scripts_restructured/core/nautilus_data_saver.py`
**Pattern**: Full Nautilus integration
- Proper Bar object conversion
- Native catalog writing
- Metadata handling
- Schema validation

## Code Duplication Analysis

### Duplicated Patterns
1. **PyArrow imports**: `import pyarrow as pa; import pyarrow.parquet as pq`
2. **Basic table writing**: `pq.write_table(table, file_path)`
3. **Metadata construction**: Manual dictionary creation for Nautilus compatibility
4. **Error handling**: Similar try/catch patterns
5. **Path handling**: Directory creation and file path construction

### Missing Standardization
1. **Compression settings**: Different files use different compression
2. **Row group sizes**: Inconsistent across implementations
3. **Metadata format**: Manual vs automated metadata creation
4. **Schema validation**: Some files check, others don't
5. **Error reporting**: Inconsistent error handling patterns

## Centralization Opportunities

### High Priority
1. **Nautilus metadata creation**: Standardize metadata format and precision handling
2. **Schema validation**: Ensure all files are Nautilus-compatible
3. **Compression settings**: Standardize compression and row group sizes
4. **Error handling**: Consistent error reporting and logging

### Medium Priority
1. **Path construction**: Standardize file naming and directory structure
2. **Table optimization**: Consistent memory usage patterns
3. **Validation**: Pre-write data validation

### Low Priority
1. **Performance monitoring**: Write operation timing
2. **File size optimization**: Automatic compression selection

## Recommended Refactoring Strategy

### Phase 1: Create Utility Functions
- `write_parquet_with_metadata()`
- `create_nautilus_schema()`
- `validate_parquet_compatibility()`
- `get_optimal_compression_settings()`

### Phase 2: Refactor Individual Scripts
- Replace direct PyArrow calls with utility functions
- Standardize error handling
- Remove code duplication

### Phase 3: Testing and Validation
- Ensure all refactored scripts maintain functionality
- Verify Nautilus compatibility
- Performance testing

## Files Requiring Refactoring

1. **user_scripts_restructured/scripts/process_data.py**
   - Replace `save_data_file()` function
   - Add proper metadata handling

2. **user_scripts_restructured/processors/consolidator.py**
   - Replace direct `pq.write_table()` calls
   - Add Nautilus metadata support

3. **user_scripts_restructured/processors/continuous_futures.py**
   - Replace manual metadata construction
   - Use centralized metadata functions

4. **user_scripts_restructured/downloaders/base_downloader.py**
   - Replace pandas-based fallback
   - Add proper Nautilus compatibility

5. **user_scripts_restructured/scripts/create_continuous_futures.py**
   - Standardize parquet operations
   - Use centralized functions

## Success Criteria

1. **Zero code duplication** for parquet writing operations
2. **100% Nautilus compatibility** for all written files
3. **Consistent metadata** across all parquet files
4. **Standardized error handling** and logging
5. **Maintained functionality** for all existing scripts
6. **Improved maintainability** through modular design
