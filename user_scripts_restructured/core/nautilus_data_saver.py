"""
Nautilus Trader Data Saver Module.

Provides modular, atomic functionality for saving market data using Nautilus Trader's
native parquet catalog system. Functions can be imported and used independently.

This module provides both high-level convenience classes and low-level utility functions
for maximum flexibility and code reuse across different scripts.
"""

import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import List, Dict, Any, Optional, Union, Tuple
from dataclasses import dataclass
import pandas as pd
import pyarrow as pa

logger = logging.getLogger(__name__)


@dataclass
class SaveResult:
    """Result of a data save operation."""
    success: bool
    bars_saved: int
    file_path: str
    error_message: str = ""
    append_mode_used: bool = False


@dataclass
class ParquetWriteConfig:
    """Configuration for parquet writing operations."""
    compression: str = 'snappy'
    use_dictionary: bool = True
    row_group_size: int = 100000
    write_statistics: bool = True
    use_compliant_nested_type: bool = False


# ============================================================================
# UTILITY FUNCTIONS - Can be imported and used independently
# ============================================================================

def create_nautilus_metadata(
    instrument_id: str,
    bar_type: str = "1-MINUTE-LAST-EXTERNAL",
    price_precision: int = 2,
    size_precision: int = 0
) -> Dict[str, str]:
    """
    Create Nautilus-compatible metadata dictionary.

    Args:
        instrument_id: Full instrument ID (e.g., "MNQ.CME")
        bar_type: Bar type specification
        price_precision: Price decimal precision
        size_precision: Size decimal precision

    Returns:
        Dictionary with Nautilus metadata
    """
    return {
        'bar_type': f"{instrument_id}-{bar_type}",
        'instrument_id': instrument_id,
        'price_precision': str(price_precision),
        'size_precision': str(size_precision)
    }


def write_parquet_with_metadata(
    table: pa.Table,
    file_path: str,
    metadata: Optional[Dict[str, str]] = None,
    config: Optional[ParquetWriteConfig] = None
) -> bool:
    """
    Write PyArrow table to parquet with optional metadata and configuration.

    Args:
        table: PyArrow table to write
        file_path: Output file path
        metadata: Optional metadata to add to schema
        config: Optional write configuration

    Returns:
        True if successful, False otherwise
    """
    try:
        import pyarrow.parquet as pq

        if config is None:
            config = ParquetWriteConfig()

        # Add metadata to schema if provided
        if metadata:
            table = table.replace_schema_metadata(metadata)

        # Ensure output directory exists
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        # Write with configuration
        pq.write_table(
            table,
            file_path,
            compression=config.compression,
            use_dictionary=config.use_dictionary,
            row_group_size=config.row_group_size,
            write_statistics=config.write_statistics,
            use_compliant_nested_type=config.use_compliant_nested_type
        )

        logger.info(f"Successfully wrote {len(table)} rows to {file_path}")
        return True

    except Exception as e:
        logger.error(f"Error writing parquet file {file_path}: {e}")
        return False


def validate_parquet_compatibility(table: pa.Table, for_nautilus: bool = True) -> Tuple[bool, List[str]]:
    """
    Validate that a PyArrow table is compatible with requirements.

    Args:
        table: PyArrow table to validate
        for_nautilus: Whether to validate for Nautilus compatibility

    Returns:
        Tuple of (is_valid, list_of_issues)
    """
    issues = []

    try:
        # Basic validation
        if len(table) == 0:
            issues.append("Table is empty")

        if len(table.column_names) == 0:
            issues.append("Table has no columns")

        # Nautilus-specific validation
        if for_nautilus:
            required_columns = ['open', 'high', 'low', 'close', 'volume', 'ts_init']
            missing_columns = [col for col in required_columns if col not in table.column_names]
            if missing_columns:
                issues.append(f"Missing required columns for Nautilus: {missing_columns}")

            # Check metadata
            if table.schema.metadata:
                metadata = dict(table.schema.metadata)
                if 'instrument_id' not in metadata:
                    issues.append("Missing 'instrument_id' in metadata")
                if 'bar_type' not in metadata:
                    issues.append("Missing 'bar_type' in metadata")

        return len(issues) == 0, issues

    except Exception as e:
        issues.append(f"Validation error: {str(e)}")
        return False, issues


def get_optimal_compression_settings(file_size_mb: float) -> ParquetWriteConfig:
    """
    Get optimal compression settings based on expected file size.

    Args:
        file_size_mb: Expected file size in MB

    Returns:
        Optimized ParquetWriteConfig
    """
    if file_size_mb < 10:
        # Small files - prioritize speed
        return ParquetWriteConfig(
            compression='snappy',
            row_group_size=50000,
            use_dictionary=True
        )
    elif file_size_mb < 100:
        # Medium files - balanced
        return ParquetWriteConfig(
            compression='snappy',
            row_group_size=100000,
            use_dictionary=True
        )
    else:
        # Large files - prioritize compression
        return ParquetWriteConfig(
            compression='zstd',
            row_group_size=200000,
            use_dictionary=True
        )


def ensure_nanosecond_timestamps(timestamp: Union[int, float, str, datetime]) -> int:
    """
    Ensure timestamp is in nanoseconds format required by Nautilus.

    Args:
        timestamp: Timestamp in various formats

    Returns:
        Timestamp in nanoseconds
    """
    if isinstance(timestamp, datetime):
        return int(timestamp.timestamp() * 1e9)
    elif isinstance(timestamp, str):
        # Try to parse as datetime
        try:
            dt = pd.to_datetime(timestamp)
            return int(dt.timestamp() * 1e9)
        except:
            # Try to parse as number
            try:
                timestamp = float(timestamp)
            except:
                raise ValueError(f"Cannot convert timestamp: {timestamp}")

    if isinstance(timestamp, (int, float)):
        # Determine if it's seconds, milliseconds, or nanoseconds
        if timestamp < 1e10:  # Seconds
            return int(timestamp * 1e9)
        elif timestamp < 1e13:  # Milliseconds
            return int(timestamp * 1e6)
        else:  # Assume nanoseconds
            return int(timestamp)

    raise ValueError(f"Cannot convert timestamp: {timestamp}")


def create_exchange_mapping() -> Dict[str, str]:
    """
    Create standard mapping of symbols to their primary exchanges.

    Returns:
        Dictionary mapping symbols to exchange codes
    """
    return {
        # Futures
        'MNQ': 'CME', 'NQ': 'CME', 'ES': 'CME', 'MES': 'CME',
        'YM': 'CBOT', 'MYM': 'CBOT', 'RTY': 'CME', 'M2K': 'CME',
        'CL': 'NYMEX', 'QM': 'NYMEX', 'GC': 'COMEX', 'MGC': 'COMEX',
        'SI': 'COMEX', 'SIL': 'COMEX',

        # Stocks
        'AAPL': 'NASDAQ', 'MSFT': 'NASDAQ', 'GOOGL': 'NASDAQ',
        'AMZN': 'NASDAQ', 'TSLA': 'NASDAQ', 'NVDA': 'NASDAQ',
        'META': 'NASDAQ', 'SPY': 'ARCA', 'QQQ': 'NASDAQ',
        'IWM': 'ARCA', 'VIX': 'CBOE',

        # Forex (using SIM for simulated/generic)
        'EURUSD': 'SIM', 'GBPUSD': 'SIM', 'USDJPY': 'SIM',
        'USDCHF': 'SIM', 'AUDUSD': 'SIM', 'USDCAD': 'SIM',
        'NZDUSD': 'SIM',
    }


def get_exchange_for_symbol(symbol: str, exchange_map: Optional[Dict[str, str]] = None) -> str:
    """
    Get the appropriate exchange for a symbol.

    Args:
        symbol: Trading symbol
        exchange_map: Optional custom exchange mapping

    Returns:
        Exchange code (defaults to 'SIM' if unknown)
    """
    if exchange_map is None:
        exchange_map = create_exchange_mapping()

    # Clean symbol - remove month/year codes for futures
    base_symbol = symbol.split('_')[0]  # Handle MNQ_202312 format
    base_symbol = ''.join(c for c in base_symbol if c.isalpha())  # Remove numbers

    return exchange_map.get(base_symbol.upper(), 'SIM')


def convert_bars_to_nautilus_format(
    bars_data: List[Dict[str, Any]],
    symbol: str,
    bar_type: str = "1-MINUTE-LAST",
    instrument_id: Optional[str] = None,
    exchange_map: Optional[Dict[str, str]] = None
) -> List:
    """
    Convert bar data to Nautilus Bar objects.

    Args:
        bars_data: List of bar dictionaries with OHLCV data
        symbol: Trading symbol
        bar_type: Bar type specification
        instrument_id: Full instrument ID (if None, will be constructed)
        exchange_map: Optional custom exchange mapping

    Returns:
        List of Nautilus Bar objects
    """
    try:
        # Import Nautilus components
        from nautilus_trader.model.data import Bar
        from nautilus_trader.model.data import BarType
        from nautilus_trader.model.identifiers import InstrumentId
        from nautilus_trader.model.objects import Price, Quantity

        # Create instrument ID if not provided
        if instrument_id is None:
            exchange = get_exchange_for_symbol(symbol, exchange_map)
            instrument_id = f"{symbol}.{exchange}"

        # Parse bar type - ensure it has all required components
        if bar_type.count('-') < 2:
            # If bar_type is like "1-MINUTE-LAST", construct proper format
            bar_type_parts = bar_type.split('-')
            if len(bar_type_parts) == 3:
                # Already complete: "1-MINUTE-LAST"
                bar_spec = bar_type
            elif len(bar_type_parts) == 2:
                # Missing price type: "1-MINUTE" -> "1-MINUTE-LAST"
                bar_spec = f"{bar_type}-LAST"
            else:
                # Assume it's just the timeframe: "1MINUTE" -> "1-MINUTE-LAST"
                bar_spec = "1-MINUTE-LAST"
        else:
            bar_spec = bar_type

        nautilus_bar_type = BarType.from_str(f"{instrument_id}-{bar_spec}-EXTERNAL")
        nautilus_bars = []

        for bar_data in bars_data:
            try:
                # Extract required fields
                ts_init = ensure_nanosecond_timestamps(bar_data.get('ts_init', bar_data.get('timestamp')))
                ts_event = ensure_nanosecond_timestamps(bar_data.get('ts_event', ts_init))

                # Create Price objects - Nautilus automatically infers precision from string format
                open_price = Price.from_str(str(bar_data['open']))
                high_price = Price.from_str(str(bar_data['high']))
                low_price = Price.from_str(str(bar_data['low']))
                close_price = Price.from_str(str(bar_data['close']))
                volume = Quantity.from_str(str(bar_data['volume']))

                # Create Nautilus Bar object
                nautilus_bar = Bar(
                    bar_type=nautilus_bar_type,
                    open=open_price,
                    high=high_price,
                    low=low_price,
                    close=close_price,
                    volume=volume,
                    ts_event=ts_event,
                    ts_init=ts_init
                )

                nautilus_bars.append(nautilus_bar)

            except Exception as e:
                logger.error(f"Error converting bar data: {e}")
                logger.debug(f"Problematic bar data: {bar_data}")
                continue

        logger.info(f"Converted {len(nautilus_bars)} bars to Nautilus format")
        return nautilus_bars

    except ImportError as e:
        logger.error(f"Failed to import Nautilus components: {e}")
        return []
    except Exception as e:
        logger.error(f"Error in bar conversion: {e}")
        return []


def save_bars_to_nautilus_catalog(
    bars_data: List[Dict[str, Any]],
    catalog_path: str,
    symbol: str,
    bar_type: str = "1-MINUTE-LAST",
    instrument_id: Optional[str] = None,
    append_mode: bool = True
) -> SaveResult:
    """
    Save bar data directly to Nautilus catalog using native methods.

    Args:
        bars_data: List of bar dictionaries with OHLCV data
        catalog_path: Path to Nautilus catalog directory
        symbol: Trading symbol
        bar_type: Bar type specification
        instrument_id: Full instrument ID (if None, will be constructed)
        append_mode: Whether to append to existing data

    Returns:
        SaveResult with operation details
    """
    try:
        from nautilus_trader.persistence.catalog.parquet import ParquetDataCatalog

        # Initialize catalog
        catalog = ParquetDataCatalog(path=catalog_path)

        # Convert to Nautilus format
        nautilus_bars = convert_bars_to_nautilus_format(
            bars_data, symbol, bar_type, instrument_id
        )

        if not nautilus_bars:
            return SaveResult(
                success=False,
                bars_saved=0,
                file_path="",
                error_message="Failed to convert bars to Nautilus format"
            )

        # Sort bars by timestamp to ensure monotonic order
        nautilus_bars.sort(key=lambda x: x.ts_init)

        # Save to catalog
        catalog.write_data(nautilus_bars)

        # Determine file path for result
        if instrument_id is None:
            exchange = get_exchange_for_symbol(symbol)
            instrument_id = f"{symbol}.{exchange}"

        from nautilus_trader.persistence.funcs import urisafe_instrument_id
        clean_instrument_id = urisafe_instrument_id(instrument_id)
        file_path = f"{catalog_path}/data/bar/{clean_instrument_id}"

        logger.info(f"Successfully saved {len(nautilus_bars)} bars to Nautilus catalog")

        return SaveResult(
            success=True,
            bars_saved=len(nautilus_bars),
            file_path=file_path,
            append_mode_used=append_mode
        )

    except Exception as e:
        logger.error(f"Error saving bars to Nautilus catalog: {e}")
        return SaveResult(
            success=False,
            bars_saved=0,
            file_path="",
            error_message=str(e)
        )


def save_table_with_nautilus_metadata(
    table: pa.Table,
    file_path: str,
    instrument_id: str,
    bar_type: str = "1-MINUTE-LAST-EXTERNAL",
    price_precision: int = 2,
    size_precision: int = 0,
    config: Optional[ParquetWriteConfig] = None
) -> bool:
    """
    Save PyArrow table with proper Nautilus metadata.

    Args:
        table: PyArrow table to save
        file_path: Output file path
        instrument_id: Instrument ID for metadata
        bar_type: Bar type for metadata
        price_precision: Price decimal precision
        size_precision: Size decimal precision
        config: Optional write configuration

    Returns:
        True if successful, False otherwise
    """
    try:
        # Create Nautilus metadata
        metadata = create_nautilus_metadata(
            instrument_id=instrument_id,
            bar_type=bar_type,
            price_precision=price_precision,
            size_precision=size_precision
        )

        # Write with metadata
        return write_parquet_with_metadata(table, file_path, metadata, config)

    except Exception as e:
        logger.error(f"Error saving table with Nautilus metadata: {e}")
        return False


def consolidate_parquet_files(
    input_files: List[str],
    output_file: str,
    instrument_id: Optional[str] = None,
    remove_duplicates: bool = True,
    sort_by_timestamp: bool = True,
    config: Optional[ParquetWriteConfig] = None
) -> Tuple[bool, Dict[str, Any]]:
    """
    Consolidate multiple parquet files into a single file with optional Nautilus metadata.

    Args:
        input_files: List of input parquet file paths
        output_file: Output file path
        instrument_id: Optional instrument ID for Nautilus metadata
        remove_duplicates: Whether to remove duplicate rows
        sort_by_timestamp: Whether to sort by timestamp
        config: Optional write configuration

    Returns:
        Tuple of (success, stats_dict)
    """
    try:
        import pyarrow.parquet as pq

        if not input_files:
            return False, {"error": "No input files provided"}

        logger.info(f"Consolidating {len(input_files)} files into {output_file}")

        # Read and combine all files
        tables = []
        total_input_rows = 0

        for file_path in input_files:
            try:
                table = pq.read_table(file_path)
                if len(table) > 0:
                    tables.append(table)
                    total_input_rows += len(table)
                    logger.debug(f"Loaded {len(table):,} rows from {file_path}")
            except Exception as e:
                logger.warning(f"Error loading {file_path}: {e}")
                continue

        if not tables:
            return False, {"error": "No valid tables loaded"}

        # Combine tables
        combined_table = pa.concat_tables(tables)
        logger.info(f"Combined {total_input_rows:,} rows from {len(tables)} files")

        # Remove duplicates if requested
        duplicates_removed = 0
        if remove_duplicates and 'ts_init' in combined_table.column_names:
            original_count = len(combined_table)
            # Remove duplicates based on timestamp
            combined_table = combined_table.group_by('ts_init').aggregate([
                ('open', 'first'),
                ('high', 'first'),
                ('low', 'first'),
                ('close', 'first'),
                ('volume', 'first'),
                ('ts_event', 'first')
            ])
            duplicates_removed = original_count - len(combined_table)
            if duplicates_removed > 0:
                logger.info(f"Removed {duplicates_removed:,} duplicate rows")

        # Sort by timestamp if requested
        if sort_by_timestamp and 'ts_init' in combined_table.column_names:
            combined_table = combined_table.sort_by('ts_init')
            logger.info("Sorted data by timestamp")

        # Add Nautilus metadata if instrument_id provided
        if instrument_id:
            metadata = create_nautilus_metadata(instrument_id=instrument_id)
            combined_table = combined_table.replace_schema_metadata(metadata)
            logger.info(f"Added Nautilus metadata for {instrument_id}")

        # Write consolidated file
        success = write_parquet_with_metadata(combined_table, output_file, config=config)

        stats = {
            "input_files": len(input_files),
            "total_input_rows": total_input_rows,
            "total_output_rows": len(combined_table),
            "duplicates_removed": duplicates_removed,
            "output_file": output_file
        }

        return success, stats

    except Exception as e:
        logger.error(f"Error consolidating parquet files: {e}")
        return False, {"error": str(e)}


def create_fallback_parquet_file(
    data: List[Dict[str, Any]],
    symbol: str,
    output_dir: str = "data",
    add_timestamp: bool = True
) -> SaveResult:
    """
    Create a fallback parquet file when Nautilus catalog is not available.

    Args:
        data: List of bar dictionaries
        symbol: Trading symbol
        output_dir: Output directory
        add_timestamp: Whether to add timestamp to filename

    Returns:
        SaveResult with operation details
    """
    try:
        import pandas as pd

        if not data:
            return SaveResult(
                success=False,
                bars_saved=0,
                file_path="",
                error_message="No data provided"
            )

        # Convert to DataFrame
        df = pd.DataFrame(data)

        # Create output directory
        output_path = Path(output_dir) / symbol
        output_path.mkdir(parents=True, exist_ok=True)

        # Generate filename
        if add_timestamp:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{symbol}_bars_{timestamp}.parquet"
        else:
            filename = f"{symbol}_bars.parquet"

        output_file = output_path / filename

        # Save as parquet with basic configuration
        config = ParquetWriteConfig()
        df_table = pa.Table.from_pandas(df)

        success = write_parquet_with_metadata(df_table, str(output_file), config=config)

        if success:
            logger.info(f"Saved {len(data)} bars to fallback file: {output_file}")
            return SaveResult(
                success=True,
                bars_saved=len(data),
                file_path=str(output_file)
            )
        else:
            return SaveResult(
                success=False,
                bars_saved=0,
                file_path="",
                error_message="Failed to write parquet file"
            )

    except Exception as e:
        logger.error(f"Error creating fallback parquet file: {e}")
        return SaveResult(
            success=False,
            bars_saved=0,
            file_path="",
            error_message=str(e)
        )


# ============================================================================
# HIGH-LEVEL CONVENIENCE CLASS
# ============================================================================

class NautilusDataSaver:
    """
    High-level convenience class for saving market data using Nautilus Trader's
    native parquet catalog system.

    This class provides a simple interface while using the modular utility functions
    internally. For more control, use the utility functions directly.
    """

    def __init__(self, catalog_path: str = "/home/<USER>/nautilus_trader_fork/catalog"):
        """
        Initialize the Nautilus data saver.

        Args:
            catalog_path: Path to Nautilus catalog directory
        """
        self.catalog_path = Path(catalog_path)
        self.catalog = None
        self.exchange_map = create_exchange_mapping()
        self._initialize_catalog()

    def _initialize_catalog(self) -> bool:
        """
        Initialize the Nautilus parquet catalog.

        Returns:
            True if initialization successful
        """
        try:
            # Import Nautilus components
            from nautilus_trader.persistence.catalog.parquet import ParquetDataCatalog

            # Create catalog directory if it doesn't exist
            self.catalog_path.mkdir(parents=True, exist_ok=True)

            # Initialize the catalog
            self.catalog = ParquetDataCatalog(path=str(self.catalog_path))

            logger.info(f"Nautilus catalog initialized at: {self.catalog_path}")
            return True

        except ImportError as e:
            logger.error(f"Failed to import Nautilus components: {e}")
            logger.error("Please ensure Nautilus Trader is properly installed")
            return False
        except Exception as e:
            logger.error(f"Failed to initialize Nautilus catalog: {e}")
            return False

    def save_bars(self,
                  bars_data: List[Dict[str, Any]],
                  symbol: str,
                  bar_type: str = "1-MINUTE-LAST",
                  instrument_id: Optional[str] = None,
                  append_mode: bool = True) -> SaveResult:
        """
        Save bar data to Nautilus catalog using utility functions.

        Args:
            bars_data: List of bar dictionaries with OHLCV data
            symbol: Trading symbol (e.g., "MNQ")
            bar_type: Bar type specification (e.g., "1-MINUTE-LAST")
            instrument_id: Full instrument ID (if None, will be constructed)
            append_mode: Whether to append to existing data

        Returns:
            SaveResult with operation details
        """
        if not self.catalog:
            return SaveResult(
                success=False,
                bars_saved=0,
                file_path="",
                error_message="Nautilus catalog not initialized"
            )

        if not bars_data:
            return SaveResult(
                success=False,
                bars_saved=0,
                file_path="",
                error_message="No bar data provided"
            )

        logger.info(f"Saving {len(bars_data)} bars for {symbol} to Nautilus catalog")

        # Use the utility function for saving
        return save_bars_to_nautilus_catalog(
            bars_data=bars_data,
            catalog_path=str(self.catalog_path),
            symbol=symbol,
            bar_type=bar_type,
            instrument_id=instrument_id,
            append_mode=append_mode
        )

    
    def _get_data_file_path(self, symbol: str, instrument_id: Optional[str] = None) -> Path:
        """Get the expected data file path in the catalog."""
        if instrument_id is None:
            exchange = get_exchange_for_symbol(symbol, self.exchange_map)
            instrument_id = f"{symbol}.{exchange}"

        from nautilus_trader.persistence.funcs import urisafe_instrument_id
        clean_instrument_id = urisafe_instrument_id(instrument_id)
        return self.catalog_path / "data" / "bar" / clean_instrument_id

    def check_existing_data(self, symbol: str, instrument_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Check for existing data in the catalog.
        
        Args:
            symbol: Trading symbol
            instrument_id: Instrument ID
            
        Returns:
            Dictionary with existing data information
        """
        if not self.catalog:
            return {"exists": False, "error": "Catalog not initialized"}

        try:
            from nautilus_trader.model.data import Bar
            
            if instrument_id is None:
                # Smart symbol resolution
                if "." in symbol and not symbol.endswith(".CME"):
                    # Extract base symbol from complex identifiers like "MNQ.CME-1-MINUTE-LAST-EXTERNAL"
                    if "-" in symbol:
                        base_symbol = symbol.split("-")[0]  # "MNQ.CME"
                        if base_symbol.endswith(".CME"):
                            instrument_id = base_symbol
                        else:
                            instrument_id = f"{base_symbol}.CME"
                    else:
                        instrument_id = symbol
                else:
                    instrument_id = f"{symbol}.CME"
            
            # Query existing data
            existing_bars = self.catalog.query(
                data_cls=Bar,
                instrument_ids=[instrument_id],
                raise_on_empty=False
            )
            
            if existing_bars:
                # Get timestamp range
                timestamps = [bar.ts_init for bar in existing_bars]
                min_ts = min(timestamps)
                max_ts = max(timestamps)
                
                # Convert to datetime
                min_dt = pd.to_datetime(min_ts, unit='ns')
                max_dt = pd.to_datetime(max_ts, unit='ns')
                
                return {
                    "exists": True,
                    "bar_count": len(existing_bars),
                    "date_range": {
                        "start": min_dt.isoformat(),
                        "end": max_dt.isoformat()
                    },
                    "latest_timestamp": max_ts
                }
            else:
                return {"exists": False, "bar_count": 0}
                
        except Exception as e:
            logger.error(f"Error checking existing data: {e}")
            return {"exists": False, "error": str(e)}
    
    def query_data(self, 
                   symbol: str, 
                   start_date: Optional[str] = None,
                   end_date: Optional[str] = None,
                   instrument_id: Optional[str] = None) -> List:
        """
        Query data from the catalog.
        
        Args:
            symbol: Trading symbol
            start_date: Start date (ISO format)
            end_date: End date (ISO format)
            instrument_id: Instrument ID
            
        Returns:
            List of Bar objects
        """
        if not self.catalog:
            logger.error("Catalog not initialized")
            return []
        
        try:
            from nautilus_trader.model.data import Bar
            
            if instrument_id is None:
                instrument_id = f"{symbol}.CME"
            
            # Convert dates to timestamps if provided
            start_ts = None
            end_ts = None
            
            if start_date:
                start_ts = pd.to_datetime(start_date).timestamp() * 1e9
            if end_date:
                end_ts = pd.to_datetime(end_date).timestamp() * 1e9
            
            # Query data
            bars = self.catalog.query(
                data_cls=Bar,
                instrument_ids=[instrument_id],
                start=start_ts,
                end=end_ts,
                raise_on_empty=False
            )
            
            logger.info(f"Queried {len(bars)} bars for {symbol}")
            return bars
            
        except Exception as e:
            logger.error(f"Error querying data: {e}")
            return []
    
    def validate_catalog(self) -> Dict[str, Any]:
        """
        Validate the Nautilus catalog.
        
        Returns:
            Validation results
        """
        validation_result = {
            "catalog_path": str(self.catalog_path),
            "is_valid": False,
            "catalog_initialized": self.catalog is not None,
            "data_types": [],
            "instrument_count": 0,
            "total_files": 0,
            "issues": []
        }
        
        try:
            if not self.catalog_path.exists():
                validation_result["issues"].append("Catalog path does not exist")
                return validation_result
            
            if not self.catalog:
                validation_result["issues"].append("Catalog not initialized")
                return validation_result
            
            # Get catalog info
            data_types = self.catalog.list_data_types()
            validation_result["data_types"] = data_types
            
            # Count files
            data_path = self.catalog_path / "data"
            if data_path.exists():
                parquet_files = list(data_path.rglob("*.parquet"))
                validation_result["total_files"] = len(parquet_files)
                
                # Count unique instruments
                instruments = set()
                for file_path in parquet_files:
                    # Extract instrument from path structure
                    parts = file_path.parts
                    if len(parts) >= 2:
                        instrument = parts[-2]  # Parent directory
                        instruments.add(instrument)
                
                validation_result["instrument_count"] = len(instruments)
            
            validation_result["is_valid"] = True
            
        except Exception as e:
            validation_result["issues"].append(f"Validation error: {str(e)}")
        
        return validation_result
    
    def consolidate_data(self, symbol: str, instrument_id: Optional[str] = None) -> bool:
        """
        Consolidate fragmented data files for better performance.
        
        Args:
            symbol: Trading symbol
            instrument_id: Instrument ID
            
        Returns:
            True if consolidation successful
        """
        if not self.catalog:
            logger.error("Catalog not initialized")
            return False
        
        try:
            from nautilus_trader.model.data import Bar
            
            if instrument_id is None:
                instrument_id = f"{symbol}.CME"
            
            logger.info(f"Consolidating data for {symbol}")
            
            # Use Nautilus catalog consolidation
            self.catalog.consolidate_data(
                data_cls=Bar,
                instrument_id=instrument_id
            )
            
            logger.info(f"Data consolidation completed for {symbol}")
            return True
            
        except Exception as e:
            logger.error(f"Error during data consolidation: {e}")
            return False
    
    def get_catalog_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive catalog statistics.
        
        Returns:
            Dictionary with catalog statistics
        """
        stats = {
            "catalog_path": str(self.catalog_path),
            "catalog_initialized": self.catalog is not None,
            "total_size_bytes": 0,
            "file_count": 0,
            "data_types": [],
            "instruments": [],
            "date_range": {"min": None, "max": None}
        }
        
        try:
            if not self.catalog_path.exists():
                return stats
            
            # Scan files
            data_path = self.catalog_path / "data"
            if data_path.exists():
                parquet_files = list(data_path.rglob("*.parquet"))
                stats["file_count"] = len(parquet_files)
                
                # Calculate total size
                total_size = sum(f.stat().st_size for f in parquet_files)
                stats["total_size_bytes"] = total_size
                
                # Extract unique instruments and data types
                instruments = set()
                data_types = set()
                
                for file_path in parquet_files:
                    parts = file_path.parts
                    if len(parts) >= 3:
                        data_type = parts[-3]  # e.g., "bar"
                        instrument = parts[-2]  # e.g., "MNQ_CME"
                        
                        data_types.add(data_type)
                        instruments.add(instrument)
                
                stats["data_types"] = list(data_types)
                stats["instruments"] = list(instruments)
            
            # Get catalog-specific stats if available
            if self.catalog:
                try:
                    catalog_data_types = self.catalog.list_data_types()
                    if catalog_data_types:
                        stats["data_types"] = list(set(stats["data_types"] + catalog_data_types))
                except:
                    pass
                
        except Exception as e:
            logger.error(f"Error getting catalog stats: {e}")
            stats["error"] = str(e)
        
        return stats
    

