"""
Data consolidation processor for combining and cleaning multiple data files.

This processor handles:
- Merging multiple parquet files
- Removing duplicates across files
- Sorting by timestamp
- Data validation and cleaning
- Gap detection and handling
- Memory-efficient processing for large datasets
"""

import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class ConsolidationResult:
    """Result of data consolidation operation."""
    success: bool
    input_files: List[str]
    output_file: str
    total_input_rows: int
    total_output_rows: int
    duplicates_removed: int
    invalid_rows_removed: int
    gaps_filled: int
    processing_time_seconds: float
    error_message: Optional[str] = None


class DataConsolidator:
    """
    Data consolidation processor for combining multiple data files.

    Features:
    - Memory-efficient processing
    - Duplicate removal
    - Data validation
    - Gap detection and filling
    - Configurable sorting and filtering
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the data consolidator.

        Args:
            config: Configuration dictionary or Config object
        """
        # Handle both Config objects and dictionaries
        if hasattr(config, 'to_dict'):
            self.config = config.to_dict()
        else:
            self.config = config or {}

        self.chunk_size = self.config.get('processing', {}).get('chunk_size', 100000)
        self.memory_limit_gb = self.config.get('processing', {}).get('memory_limit_gb', 8.0)
        self.validation_enabled = self.config.get('processing', {}).get('validation_enabled', True)

        # Import data validation if available
        try:
            from user_scripts_restructured.core.data_validation import DataValidator
            self.validator = DataValidator() if self.validation_enabled else None
        except ImportError:
            logger.warning("DataValidator not available - validation disabled")
            self.validator = None

    def consolidate_files(self, input_files: List[str], output_file: str,
                         remove_duplicates: bool = True,
                         validate_data: bool = True,
                         sort_by_timestamp: bool = True,
                         fill_gaps: bool = False) -> ConsolidationResult:
        """
        Consolidate multiple data files into a single file.

        Args:
            input_files: List of input file paths
            output_file: Output file path
            remove_duplicates: Whether to remove duplicate timestamps
            validate_data: Whether to validate data quality
            sort_by_timestamp: Whether to sort by timestamp
            fill_gaps: Whether to fill data gaps

        Returns:
            ConsolidationResult with operation details
        """
        start_time = datetime.now()

        try:
            # Check PyArrow availability
            import pyarrow as pa
            import pyarrow.parquet as pq
            import pyarrow.compute as pc
        except ImportError:
            error_msg = "PyArrow is required for data consolidation"
            logger.error(error_msg)
            return ConsolidationResult(
                success=False,
                input_files=input_files,
                output_file=output_file,
                total_input_rows=0,
                total_output_rows=0,
                duplicates_removed=0,
                invalid_rows_removed=0,
                gaps_filled=0,
                processing_time_seconds=0,
                error_message=error_msg
            )

        logger.info(f"Consolidating {len(input_files)} files into {output_file}")

        try:
            # Validate input files
            valid_files = []
            total_input_rows = 0

            for file_path in input_files:
                if not Path(file_path).exists():
                    logger.warning(f"Input file does not exist: {file_path}")
                    continue

                try:
                    # Check file and get row count
                    parquet_file = pq.ParquetFile(file_path)
                    row_count = parquet_file.metadata.num_rows
                    total_input_rows += row_count
                    valid_files.append(file_path)
                    logger.debug(f"File {file_path}: {row_count:,} rows")
                except Exception as e:
                    logger.warning(f"Error reading file {file_path}: {e}")
                    continue

            if not valid_files:
                error_msg = "No valid input files found"
                logger.error(error_msg)
                return ConsolidationResult(
                    success=False,
                    input_files=input_files,
                    output_file=output_file,
                    total_input_rows=0,
                    total_output_rows=0,
                    duplicates_removed=0,
                    invalid_rows_removed=0,
                    gaps_filled=0,
                    processing_time_seconds=0,
                    error_message=error_msg
                )

            logger.info(f"Processing {len(valid_files)} valid files with {total_input_rows:,} total rows")

            # Read and combine all files
            tables = []
            for file_path in valid_files:
                try:
                    table = pq.read_table(file_path)
                    if len(table) > 0:
                        tables.append(table)
                        logger.debug(f"Loaded {len(table):,} rows from {file_path}")
                except Exception as e:
                    logger.warning(f"Error loading {file_path}: {e}")
                    continue

            if not tables:
                error_msg = "No data loaded from input files"
                logger.error(error_msg)
                return ConsolidationResult(
                    success=False,
                    input_files=input_files,
                    output_file=output_file,
                    total_input_rows=total_input_rows,
                    total_output_rows=0,
                    duplicates_removed=0,
                    invalid_rows_removed=0,
                    gaps_filled=0,
                    processing_time_seconds=0,
                    error_message=error_msg
                )

            # Concatenate all tables
            logger.info("Concatenating tables...")
            combined_table = pa.concat_tables(tables)
            logger.info(f"Combined table has {len(combined_table):,} rows")

            # Process the combined data
            processed_table, stats = self._process_combined_data(
                combined_table,
                remove_duplicates=remove_duplicates,
                validate_data=validate_data,
                sort_by_timestamp=sort_by_timestamp,
                fill_gaps=fill_gaps
            )

            # Save the result using centralized utility
            logger.info(f"Saving consolidated data to {output_file}")

            # Try to extract instrument_id from metadata or filename
            instrument_id = None
            try:
                # Check table metadata first
                if processed_table.schema.metadata:
                    metadata = dict(processed_table.schema.metadata)
                    instrument_id = metadata.get('instrument_id')

                # If not in metadata, try to extract from filename
                if not instrument_id:
                    file_stem = Path(output_file).stem
                    if '.' in file_stem and any(exchange in file_stem for exchange in ['CME', 'NASDAQ', 'NYSE', 'CBOT', 'NYMEX', 'COMEX']):
                        instrument_id = file_stem.split('_')[0] if '_' in file_stem else file_stem
                        logger.info(f"Extracted instrument_id from filename: {instrument_id}")
            except Exception as e:
                logger.debug(f"Could not extract instrument_id: {e}")

            # Use centralized writing function
            from user_scripts_restructured.core.nautilus_data_saver import (
                write_parquet_with_metadata,
                create_nautilus_metadata,
                ParquetWriteConfig
            )

            # Create configuration for consolidated files
            config = ParquetWriteConfig(
                compression='snappy',
                use_dictionary=True,
                row_group_size=100000
            )

            # Add Nautilus metadata if instrument_id available
            metadata_dict = None
            if instrument_id:
                metadata_dict = create_nautilus_metadata(instrument_id=instrument_id)
                logger.info(f"Adding Nautilus metadata for {instrument_id}")

            # Write the consolidated file
            success = write_parquet_with_metadata(
                processed_table,
                output_file,
                metadata_dict,
                config
            )

            if not success:
                error_msg = "Failed to write consolidated file using centralized utility"
                logger.error(error_msg)
                return ConsolidationResult(
                    success=False,
                    input_files=input_files,
                    output_file=output_file,
                    total_input_rows=total_input_rows,
                    total_output_rows=0,
                    duplicates_removed=0,
                    invalid_rows_removed=0,
                    gaps_filled=0,
                    processing_time_seconds=(datetime.now() - start_time).total_seconds(),
                    error_message=error_msg
                )

            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            logger.info(f"Consolidation completed successfully in {processing_time:.1f} seconds")
            logger.info(f"Output: {len(processed_table):,} rows saved to {output_file}")

            return ConsolidationResult(
                success=True,
                input_files=valid_files,
                output_file=output_file,
                total_input_rows=total_input_rows,
                total_output_rows=len(processed_table),
                duplicates_removed=stats['duplicates_removed'],
                invalid_rows_removed=stats['invalid_rows_removed'],
                gaps_filled=stats['gaps_filled'],
                processing_time_seconds=processing_time
            )

        except Exception as e:
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()
            error_msg = f"Consolidation failed: {str(e)}"
            logger.error(error_msg)

            return ConsolidationResult(
                success=False,
                input_files=input_files,
                output_file=output_file,
                total_input_rows=total_input_rows,
                total_output_rows=0,
                duplicates_removed=0,
                invalid_rows_removed=0,
                gaps_filled=0,
                processing_time_seconds=processing_time,
                error_message=error_msg
            )

    def _process_combined_data(self, table, remove_duplicates: bool = True,
                              validate_data: bool = True,
                              sort_by_timestamp: bool = True,
                              fill_gaps: bool = False) -> Tuple[Any, Dict[str, int]]:
        """
        Process the combined data table.

        Args:
            table: PyArrow table with combined data
            remove_duplicates: Whether to remove duplicates
            validate_data: Whether to validate data
            sort_by_timestamp: Whether to sort by timestamp
            fill_gaps: Whether to fill gaps

        Returns:
            Tuple of (processed_table, statistics)
        """
        try:
            import pyarrow as pa
            import pyarrow.compute as pc
        except ImportError:
            raise ImportError("PyArrow is required for data processing")

        stats = {
            'duplicates_removed': 0,
            'invalid_rows_removed': 0,
            'gaps_filled': 0
        }

        processed_table = table
        original_count = len(table)

        logger.info("Processing combined data...")

        # Sort by timestamp if requested
        if sort_by_timestamp:
            timestamp_column = None
            for col_name in ['ts_init', 'ts_event', 'timestamp']:
                if col_name in table.column_names:
                    timestamp_column = col_name
                    break

            if timestamp_column:
                logger.info(f"Sorting by {timestamp_column}...")
                sort_indices = pc.sort_indices(table, sort_keys=[(timestamp_column, "ascending")])
                processed_table = pc.take(table, sort_indices)
                logger.info("Data sorted successfully")
            else:
                logger.warning("No timestamp column found for sorting")

        # Remove duplicates if requested
        if remove_duplicates:
            logger.info("Removing duplicates...")
            before_count = len(processed_table)

            # Find timestamp column for duplicate detection
            timestamp_column = None
            for col_name in ['ts_init', 'ts_event', 'timestamp']:
                if col_name in processed_table.column_names:
                    timestamp_column = col_name
                    break

            if timestamp_column:
                # Use PyArrow's group_by to find unique timestamps
                # This is more memory efficient than converting to pandas
                try:
                    # Create a table with row indices
                    indices = pa.array(range(len(processed_table)))
                    table_with_indices = processed_table.append_column('__row_index__', indices)

                    # Group by timestamp and take first occurrence
                    grouped = table_with_indices.group_by([timestamp_column]).aggregate([
                        ('__row_index__', 'min')
                    ])

                    # Get the indices of unique rows
                    unique_indices = grouped.column('__row_index___min')

                    # Take only the unique rows
                    processed_table = pc.take(processed_table, unique_indices)

                    after_count = len(processed_table)
                    duplicates_removed = before_count - after_count
                    stats['duplicates_removed'] = duplicates_removed

                    if duplicates_removed > 0:
                        logger.info(f"Removed {duplicates_removed:,} duplicate timestamps")
                    else:
                        logger.info("No duplicates found")

                except Exception as e:
                    logger.warning(f"Error removing duplicates: {e}")
                    # Fallback: keep original table
                    pass
            else:
                logger.warning("No timestamp column found for duplicate removal")

        # Validate data if requested
        if validate_data and self.validator:
            logger.info("Validating data quality...")
            try:
                # Convert to list of dicts for validation (memory intensive for large datasets)
                # For large datasets, we might want to process in chunks
                if len(processed_table) > self.chunk_size:
                    logger.info(f"Large dataset detected ({len(processed_table):,} rows), processing in chunks")
                    processed_table = self._validate_data_in_chunks(processed_table, stats)
                else:
                    data_list = processed_table.to_pylist()
                    valid_data, warnings = self.validator.validate_bars(data_list)

                    invalid_count = len(data_list) - len(valid_data)
                    if invalid_count > 0:
                        logger.warning(f"Removed {invalid_count:,} invalid rows")
                        stats['invalid_rows_removed'] = invalid_count

                        # Convert back to PyArrow table
                        processed_table = pa.Table.from_pylist(valid_data)
                    else:
                        logger.info("All data is valid")

            except Exception as e:
                logger.warning(f"Error during data validation: {e}")

        # Fill gaps if requested
        if fill_gaps:
            logger.info("Analyzing and filling data gaps...")
            try:
                processed_table, gaps_filled = self._fill_data_gaps(processed_table)
                stats['gaps_filled'] = gaps_filled
                if gaps_filled > 0:
                    logger.info(f"Filled {gaps_filled} data gaps")
                else:
                    logger.info("No gaps found to fill")
            except Exception as e:
                logger.warning(f"Error filling gaps: {e}")

        final_count = len(processed_table)
        total_removed = original_count - final_count

        if total_removed > 0:
            logger.info(f"Processing complete: {final_count:,} rows remaining ({total_removed:,} rows removed)")
        else:
            logger.info(f"Processing complete: {final_count:,} rows")

        return processed_table, stats

    def _validate_data_in_chunks(self, table, stats: Dict[str, int]):
        """Validate data in chunks for memory efficiency."""
        try:
            import pyarrow as pa
        except ImportError:
            return table

        if not self.validator:
            return table

        chunk_size = self.chunk_size
        total_rows = len(table)
        valid_chunks = []
        total_invalid = 0

        logger.info(f"Processing {total_rows:,} rows in chunks of {chunk_size:,}")

        for start_idx in range(0, total_rows, chunk_size):
            end_idx = min(start_idx + chunk_size, total_rows)
            chunk = table.slice(start_idx, end_idx - start_idx)

            # Convert chunk to list for validation
            chunk_data = chunk.to_pylist()
            valid_data, warnings = self.validator.validate_bars(chunk_data)

            invalid_count = len(chunk_data) - len(valid_data)
            total_invalid += invalid_count

            if valid_data:
                valid_chunks.append(pa.Table.from_pylist(valid_data))

            if (start_idx // chunk_size + 1) % 10 == 0:
                logger.info(f"Processed {end_idx:,}/{total_rows:,} rows")

        if valid_chunks:
            result_table = pa.concat_tables(valid_chunks)
            stats['invalid_rows_removed'] = total_invalid
            if total_invalid > 0:
                logger.warning(f"Removed {total_invalid:,} invalid rows during validation")
            return result_table
        else:
            logger.error("No valid data remaining after validation")
            return table

    def _fill_data_gaps(self, table) -> Tuple[Any, int]:
        """
        Fill data gaps in the time series.

        This is a simplified implementation that identifies gaps
        and can be extended with more sophisticated gap-filling strategies.
        """
        try:
            import pyarrow as pa
            import pyarrow.compute as pc
        except ImportError:
            return table, 0

        # Find timestamp column
        timestamp_column = None
        for col_name in ['ts_init', 'ts_event', 'timestamp']:
            if col_name in table.column_names:
                timestamp_column = col_name
                break

        if not timestamp_column:
            logger.warning("No timestamp column found for gap filling")
            return table, 0

        # For now, just detect gaps but don't fill them
        # This can be extended with actual gap-filling logic
        gaps_filled = 0

        # Simple gap detection (this is a placeholder for more sophisticated logic)
        if len(table) > 1:
            timestamps = table.column(timestamp_column)
            # Calculate time differences
            # This would need more sophisticated implementation for actual gap filling
            logger.debug("Gap detection completed (filling not implemented)")

        return table, gaps_filled

    def get_consolidation_info(self, input_files: List[str]) -> Dict[str, Any]:
        """
        Get information about files to be consolidated.

        Args:
            input_files: List of input file paths

        Returns:
            Dictionary with consolidation information
        """
        try:
            import pyarrow.parquet as pq
        except ImportError:
            return {"error": "PyArrow is required"}

        info = {
            "total_files": len(input_files),
            "valid_files": 0,
            "total_rows": 0,
            "total_size_bytes": 0,
            "date_range": {"min": None, "max": None},
            "common_columns": None,
            "files_info": []
        }

        all_columns = []

        for file_path in input_files:
            file_info = {"path": file_path}

            try:
                if not Path(file_path).exists():
                    file_info["error"] = "File does not exist"
                    continue

                parquet_file = pq.ParquetFile(file_path)
                metadata = parquet_file.metadata
                schema = parquet_file.schema

                file_info.update({
                    "rows": metadata.num_rows,
                    "columns": [field.name for field in schema],
                    "size_bytes": Path(file_path).stat().st_size,
                })

                info["valid_files"] += 1
                info["total_rows"] += metadata.num_rows
                info["total_size_bytes"] += file_info["size_bytes"]
                all_columns.append(set(file_info["columns"]))

                # Try to get date range
                try:
                    table = pq.read_table(file_path)
                    if len(table) > 0:
                        for col_name in ['ts_init', 'ts_event', 'timestamp']:
                            if col_name in table.column_names:
                                import pyarrow.compute as pc
                                ts_column = table.column(col_name)
                                min_ts = pc.min(ts_column).as_py()
                                max_ts = pc.max(ts_column).as_py()

                                if info["date_range"]["min"] is None or min_ts < info["date_range"]["min"]:
                                    info["date_range"]["min"] = min_ts
                                if info["date_range"]["max"] is None or max_ts > info["date_range"]["max"]:
                                    info["date_range"]["max"] = max_ts
                                break
                except Exception:
                    pass  # Skip date range analysis if it fails

            except Exception as e:
                file_info["error"] = str(e)

            info["files_info"].append(file_info)

        # Find common columns
        if all_columns:
            info["common_columns"] = list(set.intersection(*all_columns))

        return info
