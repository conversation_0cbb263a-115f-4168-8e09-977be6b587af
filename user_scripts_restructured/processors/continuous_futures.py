"""
Continuous futures processor with backward ratio adjustment.

Implements the improved approach for creating perpetual continuous futures
that reads consolidated data and applies backward ratio adjustment at
rollover points identified using contract date logic.
"""

import logging
from datetime import datetime, timedelta, date, timezone
from typing import List, Dict, Tuple, Optional, Any
from pathlib import Path
import sys

# Using full import paths from PYTHONPATH

from user_scripts_restructured.core.config import Config
from user_scripts_restructured.core.contract_utils import get_contract_for_date
from user_scripts_restructured.core.price_utils import decode_nautilus_price, encode_nautilus_price

logger = logging.getLogger(__name__)

# Nautilus Trader price precision scalars
STANDARD_PRECISION_SCALAR = 1_000_000_000
HIGH_PRECISION_SCALAR = 10_000_000_000_000_000

# Nautilus Trader price precision scalars
STANDARD_PRECISION_SCALAR = 1_000_000_000
HIGH_PRECISION_SCALAR = 10_000_000_000_000_000


class ContinuousFuturesProcessor:
    """
    Processor for creating continuous futures with backward ratio adjustment.

    This processor implements the improved approach that:
    1. Reads consolidated data containing all contracts
    2. Identifies rollover points using get_contract_for_date logic
    3. Applies backward ratio adjustment at those points
    4. Creates adjusted continuous futures suitable for backtesting
    """

    def __init__(self, config: Config):
        """
        Initialize the processor.

        Args:
            config: Configuration object
        """
        self.config = config

    def find_rollover_points_in_consolidated_data(self, consolidated_table, symbol="MNQ"):
        """
        Find rollover points in consolidated data by using the get_contract_for_date logic
        to determine when the active contract should change.

        Args:
            consolidated_table: PyArrow table with consolidated data
            symbol: Symbol prefix (e.g., "MNQ")

        Returns:
            List of tuples: [(rollover_date, old_contract_symbol, new_contract_symbol), ...]
        """
        try:
            import pyarrow.compute as pc
        except ImportError:
            logger.error("PyArrow is required for continuous futures processing")
            raise
            
        logger.info("Finding rollover points in consolidated data...")
        
        # Get all unique dates from the data
        ts_init_column = consolidated_table.column('ts_init')
        timestamps = ts_init_column.to_pylist()
        
        # Convert to dates and get unique dates
        unique_dates = set()
        for ts in timestamps:
            date_val = datetime.fromtimestamp(ts / 1e9, tz=timezone.utc).date()
            unique_dates.add(date_val)
        
        unique_dates = sorted(unique_dates)
        logger.info(f"Data spans {len(unique_dates)} unique dates from {min(unique_dates)} to {max(unique_dates)}")
        
        # Find contract changes by checking each date
        rollover_points = []
        previous_contract = None
        
        for date_val in unique_dates:
            current_contract_info = get_contract_for_date(date_val, symbol)
            current_contract = current_contract_info.symbol
            
            if previous_contract and current_contract != previous_contract:
                rollover_points.append((date_val, previous_contract, current_contract))
                logger.info(f"Rollover detected on {date_val}: {previous_contract} -> {current_contract}")
            
            previous_contract = current_contract
        
        logger.info(f"Found {len(rollover_points)} rollover points")
        return rollover_points

    def _apply_ohlc_adjustment(self, table, adjustment_factor: float, price_columns: List[str],
                              binary_length: int = 8) -> Any:
        """
        Apply adjustment factor to OHLC columns of the table.

        Args:
            table: PyArrow table
            adjustment_factor: Factor to multiply prices by
            price_columns: List of price column names
            binary_length: Binary length for encoding (8 or 16 bytes)

        Returns:
            Adjusted PyArrow table
        """
        if adjustment_factor == 1.0:
            logger.debug("No adjustment needed (factor = 1.0), returning original table")
            return table

        try:
            import pyarrow as pa
        except ImportError:
            logger.error("PyArrow is required for continuous futures processing")
            raise

        new_table = table
        for col_name in price_columns:
            if col_name not in table.column_names:
                continue

            original_column = table.column(col_name)
            col_type = table.schema.field(col_name).type

            # Decode binary prices to float for adjustment
            decoded_prices = []
            for val in original_column:
                decoded_price = decode_nautilus_price(
                    val.as_py(),
                    HIGH_PRECISION_SCALAR,
                    STANDARD_PRECISION_SCALAR
                )
                decoded_prices.append(decoded_price)

            # Apply adjustment
            adjusted_prices_float = [
                price * adjustment_factor if price is not None else None
                for price in decoded_prices
            ]

            if pa.types.is_binary(col_type) or pa.types.is_fixed_size_binary(col_type):
                final_binary_length = col_type.byte_width if pa.types.is_fixed_size_binary(col_type) else binary_length

                # Re-encode adjusted prices back to binary
                adjusted_prices_encoded = []
                for price in adjusted_prices_float:
                    if price is not None:
                        try:
                            encoded_price = encode_nautilus_price(
                                price,
                                final_binary_length,
                                HIGH_PRECISION_SCALAR,
                                STANDARD_PRECISION_SCALAR
                            )
                            adjusted_prices_encoded.append(encoded_price)
                        except Exception as e:
                            logger.warning(f"Failed to encode price {price}: {e}")
                            # Use original binary value as fallback
                            adjusted_prices_encoded.append(original_column[len(adjusted_prices_encoded)].as_py())
                    else:
                        adjusted_prices_encoded.append(None)

                # Create PyArrow array with proper handling of None values
                try:
                    new_column_array = pa.array(adjusted_prices_encoded, type=col_type)
                except pa.ArrowInvalid as e:
                    logger.warning(f"Failed to create PyArrow array for {col_name}: {e}")
                    # Fallback: keep original column
                    new_column_array = original_column

            elif pa.types.is_float64(col_type):
                new_column_array = pa.array(adjusted_prices_float, type=pa.float64())
            else:
                new_column_array = original_column  # Should not happen for price columns

            new_table = new_table.set_column(
                new_table.schema.get_field_index(col_name),
                col_name,
                new_column_array
            )

        return new_table

    def apply_backward_ratio_adjustment_to_consolidated_data(self, consolidated_table,
                                                           rollover_points: List[Tuple[date, str, str]],
                                                           symbol: str = "MNQ") -> Any:
        """
        Apply backward ratio adjustment to consolidated data at the identified rollover points.

        Args:
            consolidated_table: PyArrow table with consolidated data
            rollover_points: List of (rollover_date, old_contract, new_contract) tuples
            symbol: Symbol prefix

        Returns:
            PyArrow table with adjusted prices
        """
        logger.info("Applying backward ratio adjustment to consolidated data...")
        
        if not rollover_points:
            logger.info("No rollover points found, returning original data")
            return consolidated_table
        
        # Convert table to pandas for easier manipulation
        import pandas as pd
        df = consolidated_table.to_pandas()
        
        # Decode binary price columns to float for calculations
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in df.columns:
                # Check if the column contains binary data
                if df[col].dtype == 'object' and len(df) > 0 and isinstance(df[col].iloc[0], bytes):
                    logger.info(f"Decoding binary price column: {col}")
                    df[col] = df[col].apply(
                        lambda x: self._decode_nautilus_price(x) if isinstance(x, bytes) else x
                    )
        
        # Add a date column for easier filtering
        df['date'] = pd.to_datetime(df['ts_init'], unit='ns').dt.date
        
        # Initialize adjustment factor (starts at 1.0 for the most recent data)
        current_adjustment_factor = 1.0
        
        # Process rollover points in reverse chronological order (latest to earliest)
        for i in range(len(rollover_points) - 1, -1, -1):
            rollover_date, old_contract, new_contract = rollover_points[i]
            
            logger.info(f"Processing rollover {i+1}/{len(rollover_points)}: {old_contract} -> {new_contract} on {rollover_date}")
            
            # Get data around the rollover date to calculate ratio
            rollover_data = df[df['date'] == rollover_date]
            
            if len(rollover_data) == 0:
                logger.warning(f"No data found for rollover date {rollover_date}")
                continue
                
            # Get the first price after rollover (new contract)
            new_contract_price = None
            old_contract_price = None
            
            # For consolidated data, we estimate price change using adjacent trading days
            # This is more reliable than trying to parse same-day data
            prev_date = rollover_date - timedelta(days=1)
            next_date = rollover_date + timedelta(days=1)
            
            # Look back up to 5 days to find data (handle weekends/holidays)
            for days_back in range(1, 6):
                check_date = rollover_date - timedelta(days=days_back)
                prev_data = df[df['date'] == check_date]
                if len(prev_data) > 0:
                    # Use average of last few prices for stability
                    last_prices = prev_data.tail(min(10, len(prev_data)))['close']
                    old_contract_price = float(last_prices.mean())
                    break
            
            # Look forward up to 5 days to find data (handle weekends/holidays)  
            for days_forward in range(1, 6):
                check_date = rollover_date + timedelta(days=days_forward)
                next_data = df[df['date'] == check_date]
                if len(next_data) > 0:
                    # Use average of first few prices for stability
                    first_prices = next_data.head(min(10, len(next_data)))['close']
                    new_contract_price = float(first_prices.mean())
                    break
            
            # Fallback: use rollover date data if available
            if old_contract_price is None or new_contract_price is None:
                if len(rollover_data) > 0:
                    avg_price = float(rollover_data['close'].mean())
                    if old_contract_price is None:
                        old_contract_price = avg_price
                    if new_contract_price is None:
                        new_contract_price = avg_price
            
            if old_contract_price is not None and new_contract_price is not None and old_contract_price != 0:
                step_ratio = new_contract_price / old_contract_price
                
                # Validate ratio is reasonable (between 0.5 and 2.0)
                if not (0.5 <= step_ratio <= 2.0):
                    logger.warning(f"Suspicious step ratio {step_ratio:.6f} for rollover {old_contract} -> {new_contract}")
                    logger.warning(f"OldPrice: {old_contract_price:.2f}, NewPrice: {new_contract_price:.2f}")
                    # Skip this rollover if ratio seems unreasonable
                    continue
                    
                logger.info(f"Rollover {old_contract} -> {new_contract}: OldPrice={old_contract_price:.2f}, NewPrice={new_contract_price:.2f}, StepRatio={step_ratio:.6f}")
                
                # Apply current adjustment factor to all data before this rollover
                before_rollover_mask = df['date'] < rollover_date
                logger.info(f"Applying adjustment factor {current_adjustment_factor:.6f} to {before_rollover_mask.sum()} rows before {rollover_date}")
                
                for col in price_columns:
                    if col in df.columns:
                        df.loc[before_rollover_mask, col] = df.loc[before_rollover_mask, col] * current_adjustment_factor
                
                # Update adjustment factor for the next (earlier) period
                # For backward adjustment, we need to divide by the step ratio
                current_adjustment_factor = current_adjustment_factor / step_ratio
            else:
                logger.warning(f"Could not calculate step ratio for rollover {old_contract} -> {new_contract} on {rollover_date}")
                logger.warning(f"OldPrice: {old_contract_price}, NewPrice: {new_contract_price}")
        
        # Apply final adjustment factor to any remaining data (before the earliest rollover)
        if len(rollover_points) > 0:
            earliest_rollover_date = rollover_points[0][0]  # First rollover in chronological order
            before_earliest_mask = df['date'] < earliest_rollover_date
            
            if before_earliest_mask.sum() > 0:
                logger.info(f"Applying final adjustment factor {current_adjustment_factor:.6f} to {before_earliest_mask.sum()} rows before earliest rollover")
                for col in price_columns:
                    if col in df.columns:
                        df.loc[before_earliest_mask, col] = df.loc[before_earliest_mask, col] * current_adjustment_factor
        
        # Remove the temporary date column
        df = df.drop('date', axis=1)
        
        # Re-encode float price columns back to binary
        df_for_encoding = df
        binary_length = 16  # Our sample showed 16-byte binary format
        
        # Re-encode the float price columns to binary format matching Nautilus schema
        price_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in price_columns:
            if col in df_for_encoding.columns:
                # Check if the column is float
                if df_for_encoding[col].dtype in ['float64', 'float32']:
                    logger.info(f"Re-encoding column {col} to binary format")
                    df_for_encoding[col] = df_for_encoding[col].apply(
                        lambda x: self._encode_nautilus_price(float(x), binary_length) 
                        if pd.notna(x) else None
                    )
        
        # Convert back to PyArrow table with proper Nautilus schema
        import pyarrow as pa
        adjusted_table = pa.Table.from_pandas(df_for_encoding, preserve_index=False)
        
        # CRITICAL FIX: Ensure proper Nautilus schema with fixed_size_binary[16] for price columns
        schema_fields = []
        for field in adjusted_table.schema:
            if field.name in ['open', 'high', 'low', 'close', 'volume']:
                # Convert to fixed_size_binary[16] not null (Nautilus standard)
                schema_fields.append(pa.field(field.name, pa.binary(16), False))
            elif field.name in ['ts_event', 'ts_init']:
                # Ensure timestamp fields are not null
                schema_fields.append(pa.field(field.name, pa.uint64(), False))
            else:
                schema_fields.append(field)
        
        # Cast to proper schema
        target_schema = pa.schema(schema_fields)
        adjusted_table = adjusted_table.cast(target_schema)
        
        logger.info(f"Successfully applied backward ratio adjustment. Final table has {len(adjusted_table)} rows")
        logger.info(f"Schema after adjustment: {[f'{field.name}: {field.type}' for field in adjusted_table.schema]}")
        return adjusted_table

    def create_continuous_futures(self, consolidated_table, symbol: str = "MNQ") -> Tuple[Any, bool]:
        """
        Create continuous futures from consolidated data with backward ratio adjustment.

        Args:
            consolidated_table: PyArrow table with consolidated data
            symbol: Symbol prefix

        Returns:
            Tuple of (adjusted_table, success_flag)
        """
        try:
            import pyarrow as pa
            import pyarrow.compute as pc
        except ImportError:
            logger.error("PyArrow is required for continuous futures processing")
            return None, False

        logger.info(f"Creating continuous futures for {symbol} with backward ratio adjustment")

        # Work directly with PyArrow table
        if len(consolidated_table) == 0:
            logger.error("No data in consolidated table")
            return None, False

        # Get date range using PyArrow compute
        ts_init_column = consolidated_table.column('ts_init')

        # Convert nanoseconds to date for analysis
        min_ts = pc.min(ts_init_column).as_py()
        max_ts = pc.max(ts_init_column).as_py()

        # Convert to date objects
        min_date = datetime.fromtimestamp(min_ts / 1e9).date()
        max_date = datetime.fromtimestamp(max_ts / 1e9).date()

        logger.info(f"Data range: {min_date} to {max_date}")

        # Find rollover points directly from consolidated data
        rollover_points = self.find_rollover_points_in_consolidated_data(consolidated_table, symbol)

        logger.info(f"Found {len(rollover_points)} rollover points")
        for rollover_date, old_contract, new_contract in rollover_points:
            logger.info(f"  {rollover_date}: {old_contract} -> {new_contract}")

        # Apply backward ratio adjustment
        adjusted_table = self.apply_backward_ratio_adjustment_to_consolidated_data(
            consolidated_table, rollover_points, symbol
        )

        return adjusted_table, True

    def process_file(self, input_file: str, output_file: str, symbol: str = "MNQ") -> bool:
        """
        Process a consolidated futures file to create continuous futures.

        Args:
            input_file: Path to input consolidated file
            output_file: Path to output continuous futures file
            symbol: Symbol prefix

        Returns:
            True if successful, False otherwise
        """
        try:
            import pyarrow.parquet as pq
        except ImportError:
            logger.error("PyArrow is required for file processing")
            return False

        logger.info(f"Processing {input_file} to create continuous futures")

        try:
            # Read consolidated data
            consolidated_table = pq.read_table(input_file)
            logger.info(f"Loaded {len(consolidated_table)} rows from {input_file}")

            # Create continuous futures
            adjusted_table, success = self.create_continuous_futures(consolidated_table, symbol)

            if not success:
                logger.error("Failed to create continuous futures")
                return False

            # Save adjusted data using centralized utility
            from user_scripts_restructured.core.nautilus_data_saver import (
                write_parquet_with_metadata,
                ParquetWriteConfig
            )

            # Create configuration for continuous futures
            config = ParquetWriteConfig(
                compression='snappy',
                use_dictionary=True,
                row_group_size=200000
            )

            # Try to extract instrument_id from metadata or filename for Nautilus compatibility
            instrument_id = None
            try:
                if adjusted_table.schema.metadata:
                    metadata = dict(adjusted_table.schema.metadata)
                    instrument_id = metadata.get('instrument_id')

                if not instrument_id:
                    # Extract from filename
                    file_stem = Path(output_file).stem
                    if 'CONT' in file_stem or 'continuous' in file_stem.lower():
                        base_symbol = file_stem.split('CONT')[0] if 'CONT' in file_stem else symbol
                        instrument_id = f"{base_symbol}CONT.CME"
                        logger.info(f"Extracted continuous futures instrument_id: {instrument_id}")
            except Exception as e:
                logger.debug(f"Could not extract instrument_id: {e}")

            # Add Nautilus metadata if instrument_id available
            metadata_dict = None
            if instrument_id:
                from user_scripts_restructured.core.nautilus_data_saver import create_nautilus_metadata
                metadata_dict = create_nautilus_metadata(
                    instrument_id=instrument_id,
                    bar_type="1-MINUTE-LAST-EXTERNAL",
                    price_precision=2,
                    size_precision=0
                )
                logger.info(f"Adding Nautilus metadata for {instrument_id}")

            success = write_parquet_with_metadata(adjusted_table, output_file, metadata_dict, config)

            if not success:
                logger.error("Failed to save continuous futures using centralized utility")
                return False

            logger.info(f"Saved continuous futures to {output_file}")

            return True

        except Exception as e:
            logger.error(f"Error processing file: {e}")
            return False

    def process_table(self, consolidated_table, output_file: str, symbol: str = "MNQ") -> bool:
        """
        Process a pre-loaded consolidated futures table to create continuous futures.
        
        CRITICAL FIX: Implements smart consolidation logic to prevent duplication.
        Similar to process_data.py fix - handles existing continuous futures files.

        Args:
            consolidated_table: PyArrow table with consolidated data
            output_file: Path to output continuous futures file
            symbol: Symbol prefix

        Returns:
            True if successful, False otherwise
        """
        try:
            import pyarrow.parquet as pq
            import pyarrow as pa
            import pyarrow.compute as pc
            from pathlib import Path
        except ImportError:
            logger.error("PyArrow is required for table processing")
            return False

        logger.info(f"Processing pre-loaded table with {len(consolidated_table)} rows to create continuous futures")

        try:
            # CRITICAL FIX: Check for existing continuous futures files
            output_path = Path(output_file)
            output_dir = output_path.parent
            
            # Find existing continuous futures files in the directory
            existing_files = []
            if output_dir.exists():
                # Look for any existing MNQCONT files
                pattern_prefixes = [f"{symbol}CONT", f"consolidated_{symbol}CONT"]
                for file in output_dir.glob("*.parquet"):
                    if any(file.name.startswith(prefix) for prefix in pattern_prefixes):
                        existing_files.append(file)
            
            # Handle existing continuous futures data with atomic transactions
            existing_data = None
            backup_files = []
            if existing_files:
                logger.info(f"Found {len(existing_files)} existing continuous futures files")
                # Read the most recent existing file
                existing_file = max(existing_files, key=lambda f: f.stat().st_mtime)
                logger.info(f"Reading existing continuous futures data: {existing_file.name}")
                existing_data = pq.read_table(str(existing_file))
                logger.info(f"Existing data: {len(existing_data)} rows")
                
                # ATOMIC TRANSACTION: Create backups before removing files
                import shutil
                for old_file in existing_files:
                    backup_path = old_file.with_suffix('.backup.parquet')
                    logger.info(f"Creating backup: {backup_path.name}")
                    shutil.copy2(str(old_file), str(backup_path))
                    backup_files.append((old_file, backup_path))
                
                # Note: Remove old files only after successful processing

            # Create continuous futures from new input data
            adjusted_table, success = self.create_continuous_futures(consolidated_table, symbol)

            if not success:
                logger.error("Failed to create continuous futures")
                return False

            # Combine with existing data if it exists
            final_table = adjusted_table
            if existing_data is not None:
                logger.info("Combining with existing continuous futures data...")
                
                # CRITICAL FIX: Normalize schemas before combining
                def normalize_schema(table: pa.Table) -> pa.Table:
                    """Convert legacy binary columns to fixed_size_binary[16] to match Nautilus format"""
                    schema = table.schema
                    new_fields = []
                    needs_conversion = False
                    
                    for field in schema:
                        if field.name in ['open', 'high', 'low', 'close', 'volume']:
                            if str(field.type) == 'binary' or 'fixed_size_binary[16]' in str(field.type):
                                # Normalize to fixed_size_binary[16] not null (Nautilus standard)
                                new_field = pa.field(field.name, pa.binary(16), False)  # False = not null
                                new_fields.append(new_field)
                                if str(field.type) == 'binary':
                                    needs_conversion = True
                                    logger.info(f"Converting {field.name} from binary to fixed_size_binary[16] not null")
                                elif field.nullable:
                                    needs_conversion = True
                                    logger.info(f"Converting {field.name} to not null")
                            else:
                                new_fields.append(field)
                        elif field.name in ['ts_event', 'ts_init']:
                            # Ensure timestamp fields are not null
                            new_field = pa.field(field.name, field.type, False)  # False = not null
                            new_fields.append(new_field)
                            if field.nullable:
                                needs_conversion = True
                                logger.info(f"Converting {field.name} to not null")
                        else:
                            new_fields.append(field)
                    
                    if needs_conversion:
                        new_schema = pa.schema(new_fields)
                        return table.cast(new_schema)
                    return table
                
                # Normalize both tables to ensure schema compatibility
                existing_data = normalize_schema(existing_data)
                adjusted_table = normalize_schema(adjusted_table)
                
                # Now combine tables with compatible schemas
                combined_table = pa.concat_tables([existing_data, adjusted_table])
                
                # Sort by timestamp
                indices = pc.sort_indices(combined_table, sort_keys=[('ts_event', 'ascending')])
                sorted_table = combined_table.take(indices)
                
                # Remove duplicates (keep last occurrence for most recent adjustments)
                seen_timestamps = {}
                for i in range(len(sorted_table)):
                    ts_value = sorted_table['ts_event'][i].as_py()
                    seen_timestamps[ts_value] = i  # This keeps the last occurrence
                
                # Create deduplicated table
                result_indices = sorted(seen_timestamps.values())
                final_table = sorted_table.take(result_indices)
                
                removed_count = len(sorted_table) - len(final_table)
                logger.info(f"Combined data: {len(combined_table)} → {len(final_table)} (removed {removed_count} duplicates)")

            # CRITICAL FIX: Add proper Nautilus metadata (same approach as working manual fix)
            # Extract instrument_id from path
            output_path = Path(output_file)
            instrument_dir = output_path.parent.name if output_path.parent else ""
            if '.' in instrument_dir and '-' in instrument_dir:
                instrument_id = instrument_dir.split('-')[0]  # "MNQCONT.CME"
            else:
                instrument_id = f"{symbol}CONT.CME"
            
            logger.info(f"Using instrument_id: {instrument_id}")
            
            # Save final consolidated continuous futures using centralized utility
            from user_scripts_restructured.core.nautilus_data_saver import (
                save_table_with_nautilus_metadata,
                ParquetWriteConfig
            )

            # Create optimized config for continuous futures files
            config = ParquetWriteConfig(
                compression='snappy',
                use_dictionary=True,
                row_group_size=200000  # Larger row groups for continuous futures
            )

            # Save with proper Nautilus metadata using centralized function
            success = save_table_with_nautilus_metadata(
                table=final_table,
                file_path=output_file,
                instrument_id=instrument_id,
                bar_type="1-MINUTE-LAST-EXTERNAL",
                price_precision=2,
                size_precision=0,
                config=config
            )

            if not success:
                logger.error("Failed to save continuous futures file using centralized utility")
                return False

            logger.info(f"✓ Successfully saved {len(final_table)} continuous futures bars with proper Nautilus metadata")
            logger.info(f"Saved to: {output_file}")

            # ATOMIC COMMIT: Remove old files and clean up backups after successful save
            try:
                for old_file, backup_path in backup_files:
                    if old_file.exists():
                        logger.info(f"Removing old file: {old_file.name}")
                        old_file.unlink()
                    if backup_path.exists():
                        logger.info(f"Cleaning up backup: {backup_path.name}")
                        backup_path.unlink()
                logger.info("✓ Successfully committed all changes")
            except Exception as cleanup_error:
                logger.warning(f"Warning during cleanup: {cleanup_error}")
                # Don't fail the operation for cleanup issues

            return True

        except Exception as e:
            logger.error(f"✗ Critical error during continuous futures processing: {e}")
            import traceback
            logger.debug(traceback.format_exc())
            
            # EMERGENCY ROLLBACK: Restore backups if processing fails
            try:
                logger.info("Attempting emergency rollback...")
                for old_file, backup_path in backup_files:
                    if backup_path.exists():
                        if not old_file.exists():  # Only restore if original was removed
                            import shutil
                            shutil.move(str(backup_path), str(old_file))
                            logger.info(f"✓ Restored: {old_file.name}")
                        else:
                            backup_path.unlink()  # Clean up backup if original still exists
                logger.info("✓ Emergency rollback completed")
            except Exception as rollback_error:
                logger.error(f"✗ Emergency rollback failed: {rollback_error}")
                logger.error("CRITICAL: Manual intervention may be required")
            
            return False

    def _decode_nautilus_price(self, binary_value):
        """Decode a Nautilus binary price to float."""
        if isinstance(binary_value, bytes):
            try:
                if len(binary_value) == 16:
                    raw_value = int.from_bytes(binary_value, byteorder='little', signed=True)
                    return raw_value / HIGH_PRECISION_SCALAR
                elif len(binary_value) == 8:
                    raw_value = int.from_bytes(binary_value, byteorder='little', signed=True)
                    return raw_value / STANDARD_PRECISION_SCALAR
                else:
                    logger.warning(f"Unexpected binary length for decoding: {len(binary_value)}")
                    return 0.0
            except Exception as e:
                logger.warning(f"Failed to decode binary price: {e}")
                return 0.0
        else:
            try:
                return float(binary_value)
            except (TypeError, ValueError):
                logger.warning(f"Failed to convert {binary_value} to float for decoding")
                return 0.0
                
    def _encode_nautilus_price(self, float_value, binary_length):
        """Encode a float value to Nautilus binary price format."""
        scalar = HIGH_PRECISION_SCALAR if binary_length == 16 else STANDARD_PRECISION_SCALAR
        fixed_value = int(round(float_value * scalar))  # Use round for better precision
        return fixed_value.to_bytes(binary_length, byteorder='little', signed=True)
