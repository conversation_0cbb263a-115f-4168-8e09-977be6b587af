#!/usr/bin/env python3
"""
<PERSON><PERSON>t to create continuous futures with backward ratio adjustment.

This script implements the improved approach for creating perpetual continuous futures:
- Reads consolidated futures data (e.g., MNQ.CME file)
- Uses get_contract_for_date logic to identify rollover points
- Applies backward ratio adjustment at those rollover points
- Saves the adjusted continuous futures suitable for backtesting

Usage:
    python scripts/create_continuous_futures.py --input data/MNQ.CME.parquet --output data/MNQ_continuous.parquet --symbol MNQ
"""

import sys
import argparse
import logging
from pathlib import Path
from datetime import datetime

# Using full import paths from PYTHONPATH

try:
    from user_scripts_restructured.core.config import ConfigManager
    from user_scripts_restructured.processors.continuous_futures import ContinuousFuturesProcessor
    from user_scripts_restructured.utils.logging_utils import setup_logging
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure you're running from the correct directory")
    sys.exit(1)

logger = logging.getLogger(__name__)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Create continuous futures with backward ratio adjustment",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Create continuous MNQ futures
  python scripts/create_continuous_futures.py --input data/MNQ.CME.parquet --output data/MNQ_continuous.parquet

  # Create continuous ES futures with custom symbol
  python scripts/create_continuous_futures.py --input data/ES.CME.parquet --output data/ES_continuous.parquet --symbol ES

  # Specify custom configuration
  python scripts/create_continuous_futures.py --input data/MNQ.CME.parquet --output data/MNQ_continuous.parquet --config custom.yaml
        """
    )

    parser.add_argument(
        "--input", "-i",
        required=True,
        help="Input consolidated futures file (e.g., MNQ.CME.parquet)"
    )

    parser.add_argument(
        "--output", "-o",
        required=False,
        help="Output continuous futures file (e.g., MNQ_continuous.parquet). If omitted, defaults under catalog/data/bar/<SYMBOL>CONT.<VENUE>-1-MINUTE-LAST-EXTERNAL"
    )

    parser.add_argument(
        "--symbol", "-s",
        default="MNQ",
        help="Futures symbol prefix (default: MNQ)"
    )

    parser.add_argument(
        "--config", "-c",
        help="Configuration file path (optional)"
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )

    parser.add_argument(
        "--log-file",
        help="Log file path (optional)"
    )

    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Analyze data without creating output file"
    )

    return parser.parse_args()


def validate_inputs(args):
    """Validate input arguments."""
    input_path = Path(args.input)
    if not input_path.exists():
        raise FileNotFoundError(f"Input file does not exist: {args.input}")

    if not input_path.suffix.lower() in ['.parquet', '.pq']:
        logger.warning(f"Input file does not have .parquet extension: {args.input}")

    output_path = Path(args.output)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    if output_path.exists() and not args.dry_run:
        logger.warning(f"Output file already exists and will be overwritten: {args.output}")


def analyze_data_efficient(input_file: str, symbol: str, table=None):
    """Analyze data efficiently, optionally reusing existing table."""
    try:
        import pyarrow as pa
        import pyarrow.parquet as pq
        import pyarrow.compute as pc
    except ImportError:
        logger.error("PyArrow is required for data analysis")
        return None

    logger.info("Analyzing input data...")

    try:
        # Read metadata first for file stats
        parquet_file = pq.ParquetFile(input_file)
        metadata = parquet_file.metadata

        logger.info(f"File size: {metadata.serialized_size:,} bytes")
        logger.info(f"Number of row groups: {metadata.num_row_groups}")
        logger.info(f"Total rows: {metadata.num_rows:,}")

        # Read schema
        schema = parquet_file.schema
        logger.info(f"Columns: {[field.name for field in schema]}")

        # Check for Nautilus compatibility using centralized validation
        try:
            from user_scripts_restructured.core.nautilus_data_saver import validate_parquet_compatibility

            # Use existing table if provided, otherwise read
            if table is None:
                table = pq.read_table(input_file)

            if len(table) == 0:
                logger.warning("Input file is empty")
                return table

            # Validate Nautilus compatibility
            is_valid, issues = validate_parquet_compatibility(table, for_nautilus=True)
            if not is_valid:
                logger.warning("Input file has Nautilus compatibility issues:")
                for issue in issues:
                    logger.warning(f"  - {issue}")
            else:
                logger.info("✓ Input file is Nautilus-compatible")

        except ImportError:
            logger.debug("Centralized validation not available, using basic validation")
            # Use existing table if provided, otherwise read
            if table is None:
                table = pq.read_table(input_file)

            if len(table) == 0:
                logger.warning("Input file is empty")
                return table

        # Analyze date range using PyArrow compute
        ts_init_column = table.column('ts_init')
        min_ts = pc.min(ts_init_column).as_py()
        max_ts = pc.max(ts_init_column).as_py()

        # Convert to date objects
        from datetime import datetime
        min_date = datetime.fromtimestamp(min_ts / 1e9).date()
        max_date = datetime.fromtimestamp(max_ts / 1e9).date()
        total_days = (max_date - min_date).days + 1

        logger.info(f"Date range: {min_date} to {max_date} ({total_days} days)")

        # Calculate unique trading dates (not timestamps)
        # Convert nanosecond timestamps to dates and count unique
        try:
            # Use pandas for more reliable timestamp conversion
            import pandas as pd
            
            # Convert to pandas series for timestamp handling
            ts_series = ts_init_column.to_pandas()
            dates_series = pd.to_datetime(ts_series, unit='ns').dt.date
            unique_dates = dates_series.nunique()
            logger.info(f"Unique trading dates: {unique_dates}")
        except Exception as e:
            logger.warning(f"Could not calculate unique dates: {e}")
            # Fallback - estimate from total bars (assuming ~390 bars per trading day)
            estimated_days = len(table) // 390
            logger.info(f"Estimated trading dates: ~{estimated_days}")
        
        logger.info(f"Total bars: {len(table):,}")

        # Analyze price data
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in table.column_names:
                column = table.column(col)
                col_type = table.schema.field(col).type

                # Check if binary or float
                if pa.types.is_binary(col_type) or pa.types.is_fixed_size_binary(col_type):
                    logger.info(f"{col.title()} column: Binary format ({col_type})")
                else:
                    logger.info(f"{col.title()} column: {col_type}")
                    try:
                        min_val = pc.min(column).as_py()
                        max_val = pc.max(column).as_py()
                        logger.info(f"  Range: {min_val:.2f} to {max_val:.2f}")
                    except Exception:
                        logger.info(f"  Range: Unable to calculate")

        # Analyze volume
        if 'volume' in table.column_names:
            volume_column = table.column('volume')
            try:
                min_vol = pc.min(volume_column).as_py()
                max_vol = pc.max(volume_column).as_py()
                logger.info(f"Volume range: {min_vol:,} to {max_vol:,}")
            except Exception:
                logger.info(f"Volume range: Unable to calculate")

        logger.info("Input data analysis complete")
        return table

    except Exception as e:
        logger.error(f"Error analyzing input data: {e}")
        return None


def analyze_input_data(input_file: str, symbol: str):
    """Legacy wrapper for backward compatibility."""
    analyze_data_efficient(input_file, symbol, table=None)


def generate_default_output_path(input_path: str, symbol: str) -> str:
    """
    Generate default output path using smart consolidation logic.
    
    CRITICAL FIX: Uses fixed filename to prevent duplication issue.
    This matches the smart consolidation pattern from process_data.py.
    """
    try:
        from pathlib import Path
        
        # Determine project root (two levels up from script)
        root = Path(__file__).resolve().parents[2]
        
        # Default values
        venue = "CME"
        
        # Try to extract venue from input directory name
        inp = Path(input_path)
        input_dir = inp.parent.name
        
        try:
            # Simple pattern matching for venue extraction
            # Expected format: MNQ.CME-1-MINUTE-LAST-EXTERNAL
            if "." in input_dir and "-" in input_dir:
                first_part = input_dir.split("-")[0]  # Get "MNQ.CME" part
                if "." in first_part:
                    parts = first_part.split(".")
                    if len(parts) >= 2:
                        venue = parts[1]  # Extract "CME"
                        logger.debug(f"Extracted venue '{venue}' from directory '{input_dir}'")
        except Exception as e:
            logger.debug(f"Could not extract venue from directory name: {e}")
            # Keep default venue = "CME"
        
        # CRITICAL FIX: Use fixed filename to prevent duplication
        # Use consolidated pattern to match smart consolidation logic
        file_name = f"consolidated_{symbol}CONT.parquet"
        
        # Build output directory and file path
        output_dir = root / 'catalog' / 'data' / 'bar' / f"{symbol}CONT.{venue}-1-MINUTE-LAST-EXTERNAL"
        output_dir.mkdir(parents=True, exist_ok=True)
        output_path = output_dir / file_name
        
        logger.debug(f"Generated default output path: {output_path}")
        return str(output_path)
        
    except Exception as e:
        # Ultimate fallback
        logger.warning(f"Error generating default output path: {e}")
        root = Path(__file__).resolve().parents[2]
        fallback_path = root / 'catalog' / 'data' / 'bar' / f"consolidated_{symbol}CONT.parquet"
        logger.warning(f"Using fallback path: {fallback_path}")
        return str(fallback_path)


def main():
    """Main function."""
    args = parse_args()
    
    # Generate default output path if not provided
    if not args.output:
        args.output = generate_default_output_path(args.input, args.symbol)
        logger.info(f"No --output specified, defaulting to {args.output}")

    # Setup logging
    if args.log_file:
        setup_logging(level=args.log_level, log_file=args.log_file)
    else:
        setup_logging(level=args.log_level, console=True)

    logger.info("=" * 60)
    logger.info("CONTINUOUS FUTURES CREATION")
    logger.info("=" * 60)
    logger.info(f"Input: {args.input}")
    logger.info(f"Output: {args.output}")
    logger.info(f"Symbol: {args.symbol}")
    logger.info(f"Dry run: {args.dry_run}")

    try:
        # Validate inputs
        validate_inputs(args)

        # Load configuration
        config = ConfigManager.load_config(args.config)
        logger.info("Configuration loaded successfully")

        # Analyze input data efficiently (read table once)
        input_table = analyze_data_efficient(args.input, args.symbol)
        if input_table is None:
            logger.error("Failed to analyze input data")
            return 1

        if args.dry_run:
            logger.info("Dry run mode - analysis complete, no output file created")
            return 0

        # Create processor
        processor = ContinuousFuturesProcessor(config)

        # Process the file (reuse the table we already loaded)
        logger.info("Starting continuous futures creation...")
        start_time = datetime.now()

        success = processor.process_table(input_table, args.output, args.symbol)

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        if success:
            logger.info("✓ Continuous futures creation completed successfully")
            logger.info(f"Processing time: {duration:.1f} seconds")
            logger.info(f"Output saved to: {args.output}")

            # Validate output file using centralized utilities
            logger.info("Validating output data...")
            try:
                from user_scripts_restructured.core.nautilus_data_saver import validate_parquet_compatibility
                import pyarrow.parquet as pq

                output_table = pq.read_table(args.output)
                is_valid, issues = validate_parquet_compatibility(output_table, for_nautilus=True)

                if is_valid:
                    logger.info("✓ Output file is Nautilus-compatible")
                else:
                    logger.warning("Output file has compatibility issues:")
                    for issue in issues:
                        logger.warning(f"  - {issue}")

                # Analyze output file
                analyze_input_data(args.output, args.symbol)

            except ImportError:
                logger.debug("Centralized validation not available, using basic analysis")
                analyze_input_data(args.output, args.symbol)
            except Exception as e:
                logger.warning(f"Error validating output: {e}")
                # Still analyze the file
                analyze_input_data(args.output, args.symbol)

            return 0
        else:
            logger.error("✗ Continuous futures creation failed")
            return 1

    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Error: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main())
