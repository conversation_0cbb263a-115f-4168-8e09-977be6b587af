#!/usr/bin/env python3
"""
Data processing script for cleaning and validating downloaded data.

This script processes raw downloaded data to:
- Remove duplicates
- Validate data quality
- Clean outliers (optional)
- Consolidate multiple files into a single file
- Generate quality reports

Usage:
    # Process single file
    python scripts/process_data.py --input MNQ_raw.parquet --output MNQ_clean.parquet --remove-duplicates
    
    # Process directory (individual files)
    python scripts/process_data.py --input raw_data/ --output clean_data/ --validate --report
    
    # Consolidate multiple files in a directory
    python scripts/process_data.py --input raw_data/ --consolidate --remove-duplicates --dry-run
    
    # Full processing with validation and reporting
    python scripts/process_data.py --input raw_data/ --output clean_data/ --remove-duplicates --validate --clean-outliers --report
"""

import sys
import argparse
import logging
import struct
import shutil
from pathlib import Path
from datetime import datetime
import os

# Using full import paths from PYTHONPATH

try:
    from user_scripts_restructured.core.config import ConfigManager
    from user_scripts_restructured.core.data_validation import DataValidator, QualityMetrics
    from user_scripts_restructured.utils.logging_utils import setup_logging
    from user_scripts_restructured.utils.file_utils import ensure_directory, find_files, backup_file
    from user_scripts_restructured.utils.file_manager import DataFileManager
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure you're running from the correct directory")
    sys.exit(1)

logger = logging.getLogger(__name__)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Process and clean downloaded financial data",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process single file
  python scripts/process_data.py --input data/MNQ_raw.parquet --output data/MNQ_clean.parquet

  # Process directory with duplicate removal
  python scripts/process_data.py --input raw_data/ --output clean_data/ --remove-duplicates

  # Full processing with validation and reporting
  python scripts/process_data.py --input raw_data/ --output clean_data/ --remove-duplicates --validate --clean-outliers --report

  # Generate quality report only
  python scripts/process_data.py --input data/MNQ.parquet --report-only --output-report quality_report.txt
        """
    )
    
    parser.add_argument(
        "--input", "-i",
        required=True,
        help="Input file or directory containing raw data"
    )
    
    parser.add_argument(
        "--output", "-o",
        help="Output file or directory for processed data"
    )
    
    parser.add_argument(
        "--remove-duplicates",
        action="store_true",
        help="Remove duplicate timestamps"
    )
    
    parser.add_argument(
        "--consolidate",
        action="store_true", 
        help="Consolidate multiple files into a single file (requires directory input)"
    )
    
    parser.add_argument(
        "--validate",
        action="store_true",
        help="Validate data quality and remove invalid bars"
    )
    
    parser.add_argument(
        "--clean-outliers",
        choices=["remove", "flag", "cap"],
        help="How to handle outliers: remove, flag, or cap"
    )
    
    parser.add_argument(
        "--report",
        action="store_true",
        help="Generate quality report"
    )
    
    parser.add_argument(
        "--report-only",
        action="store_true",
        help="Only generate quality report, don't process data"
    )
    
    parser.add_argument(
        "--output-report",
        help="Output file for quality report (default: console)"
    )
    
    parser.add_argument(
        "--backup",
        action="store_true",
        help="Create backup of original files before processing"
    )
    
    parser.add_argument(
        "--config", "-c",
        help="Configuration file path (optional)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    
    parser.add_argument(
        "--log-file",
        help="Log file path (optional)"
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without actually processing"
    )
    
    parser.add_argument(
        "--auto-rename",
        action="store_true",
        help="Automatically rename files to reflect actual date ranges after processing"
    )
    
    parser.add_argument(
        "--no-auto-rename",
        action="store_true",
        help="Disable automatic file renaming (overrides config setting)"
    )
    
    parser.add_argument(
        "--preserve-raw",
        action="store_true",
        default=True,
        help="Preserve original raw files after consolidation (default: True)"
    )
    
    parser.add_argument(
        "--remove-originals",
        action="store_true",
        help="Remove original files after successful consolidation (overrides --preserve-raw)"
    )
    
    # Archive functionality - Phase 3 enhancement
    parser.add_argument(
        "--archive-mode",
        action="store_true",
        help="Move processed files to archive instead of preserving in place (new in refactoring)"
    )
    
    parser.add_argument(
        "--archive-path",
        help="Archive directory path (default: auto-detect based on data structure)"
    )
    
    return parser.parse_args()


def move_files_to_archive(files, archive_path: str, source_directory: str, dry_run: bool = False):
    """
    Move files to archive with atomic safety using FileOrganizer patterns.
    
    Args:
        files: List of Path objects to move
        archive_path: Archive directory path
        source_directory: Source directory for context
        dry_run: If True, only show what would be done
        
    Returns:
        bool: True if successful
    """
    from pathlib import Path
    
    try:
        # Auto-detect archive path if not provided
        if not archive_path:
            # Try to detect structure like catalog/data/bar/MNQ.CME-1-MINUTE-LAST-EXTERNAL
            source_path = Path(source_directory)
            if 'catalog' in source_path.parts:
                # Build archive path: data-archive/MNQ/historical/
                symbol_part = source_path.name.split('.')[0] if '.' in source_path.name else 'MNQ'
                archive_path = source_path.parent.parent.parent / 'data-archive' / symbol_part / 'historical'
            else:
                # Fallback: create archive directory next to source
                archive_path = source_path.parent / 'archive' / source_path.name
        
        archive_dir = Path(archive_path)
        
        if dry_run:
            logger.info(f"[DRY-RUN] Would create archive directory: {archive_dir}")
            for file in files:
                logger.info(f"[DRY-RUN] Would move {file.name} to {archive_dir}")
            return True
        
        # Create archive directory
        archive_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Created archive directory: {archive_dir}")
        
        # Move files with verification
        moved_files = []
        for file in files:
            try:
                dest_file = archive_dir / file.name
                # Handle file conflicts by adding timestamp
                if dest_file.exists():
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    name_parts = file.stem, timestamp, file.suffix
                    dest_file = archive_dir / f"{name_parts[0]}_{name_parts[1]}{name_parts[2]}"
                
                shutil.move(str(file), str(dest_file))
                moved_files.append((file, dest_file))
                logger.info(f"Moved {file.name} to archive: {dest_file.name}")
                
            except Exception as e:
                logger.error(f"Failed to move {file.name} to archive: {e}")
                # Rollback previous moves
                for moved_file, dest in moved_files:
                    try:
                        shutil.move(str(dest), str(moved_file))
                        logger.info(f"Rolled back {dest.name}")
                    except Exception:
                        pass
                return False
        
        logger.info(f"✓ Successfully moved {len(files)} files to archive: {archive_dir}")
        return True
        
    except Exception as e:
        logger.error(f"Archive operation failed: {e}")
        return False


def consolidate_files(directory: str, ts_column: str = "ts_event", keep_duplicate: str = "first", dry_run: bool = False, preserve_raw: bool = True, archive_mode: bool = False, archive_path: str = None):
    """
    Consolidate multiple parquet files in a directory into a single file.
    
    Args:
        directory: Directory containing parquet files to consolidate
        ts_column: Timestamp column to use for sorting and deduplication  
        keep_duplicate: Strategy for handling duplicates ('first' or 'last')
        dry_run: If True, only print what would be done without making changes
        preserve_raw: If True, preserve original files after consolidation (default: True)
        archive_mode: If True, move processed files to archive instead of preserving/deleting
        archive_path: Archive directory path (auto-detected if not provided)
        
    Returns:
        bool: True if consolidation was successful
    """
    import pyarrow as pa
    import pyarrow.parquet as pq
    import pyarrow.compute as pc
    import tempfile
    
    all_parquet_files = list(Path(directory).glob("*.parquet"))
    
    # CRITICAL FIX: Separate consolidated files from raw files to avoid duplication
    # Recognize both "consolidated_" prefix AND MNQCONT/continuous futures files as consolidated
    consolidated_files = []
    raw_files = []
    
    for f in all_parquet_files:
        is_consolidated = (
            f.name.startswith('consolidated_') or
            f.name.startswith(('MNQCONT.', 'ESCONT.', 'NQCONT.')) or
            'CONT.CME-' in f.name
        )
        if is_consolidated:
            consolidated_files.append(f)
        else:
            raw_files.append(f)
    
    logger.info(f"Found {len(all_parquet_files)} total files: {len(consolidated_files)} consolidated, {len(raw_files)} raw")
    
    # If no raw files to process, skip consolidation
    if len(raw_files) == 0:
        logger.info("No raw files to consolidate - skipping")
        return True
    
    # CRITICAL FIX: Handle ALL existing consolidated files to prevent data loss
    existing_data_tables = []
    backup_paths = []
    
    if consolidated_files:
        logger.info(f"Found {len(consolidated_files)} existing consolidated files")
        
        # Read ALL consolidated files to preserve all historical data
        total_existing_rows = 0
        for i, consolidated_file in enumerate(consolidated_files):
            logger.info(f"Reading existing consolidated file {i+1}/{len(consolidated_files)}: {consolidated_file.name}")
            
            # Extract date range from filename for reference  
            existing_date_range = consolidated_file.name.replace('consolidated_', '').replace('.parquet', '')
            
            # Read consolidated file data
            existing_data = pq.read_table(str(consolidated_file), memory_map=True, pre_buffer=False)
            existing_data_tables.append(existing_data)
            total_existing_rows += len(existing_data)
            logger.info(f"  {consolidated_file.name}: {len(existing_data)} rows, date range: {existing_date_range}")
            
            # Create backup with timestamp for atomic transaction safety
            from datetime import datetime as dt
            timestamp = dt.now().strftime("%Y%m%d_%H%M%S")
            backup_path = consolidated_file.with_suffix(f'.backup_{timestamp}.parquet')
            logger.info(f"  Creating backup: {backup_path.name}")
            shutil.copy2(str(consolidated_file), str(backup_path))
            backup_paths.append((consolidated_file, backup_path))
        
        logger.info(f"Total existing consolidated data: {total_existing_rows} rows from {len(consolidated_files)} files")
        
        # If no new raw files, nothing to do
        if len(raw_files) == 0:
            logger.info("No new raw files to add to existing consolidated files - skipping")
            # Clean up backups since no processing needed
            for _, backup_path in backup_paths:
                backup_path.unlink()
            return True
            
        logger.info(f"Will combine {len(consolidated_files)} existing consolidated files with {len(raw_files)} new raw files")
    
    # Process only raw files for new data
    parquet_files = raw_files
    logger.info(f"Processing {len(parquet_files)} raw files in {directory}")
    
    if dry_run:
        logger.info(f"DRY RUN: Would process {len(parquet_files)} raw files in {directory}")
        if existing_data_tables:
            logger.info(f"DRY RUN: Would combine with {len(existing_data_tables)} existing consolidated files")
        # Clean up dry-run backups
        for _, backup_path in backup_paths:
            if backup_path.exists():
                backup_path.unlink()
        return True
    
    try:
        # Read ONLY raw files (not existing consolidated data)
        tables = []
        schemas = []
        total_raw_rows = 0
        
        logger.info("Reading raw files for processing...")
        
        # Read new raw files
        for file in parquet_files:
            logger.info(f"Reading {file.name}")
            table = pq.read_table(str(file), memory_map=True, pre_buffer=False)
            tables.append(table)
            schemas.append(table.schema)
            total_raw_rows += len(table)
            
        # Check for schema compatibility and unify if needed
        target_schema = schemas[0]
        needs_unification = False
        
        for i, schema in enumerate(schemas[1:], 1):
            if not schema.equals(target_schema):
                logger.warning(f"Schema mismatch detected in file {parquet_files[i].name}")
                needs_unification = True
                
        if needs_unification:
            logger.info("Unifying schemas to match Nautilus binary format...")
            # Use the schema with fixed_size_binary format as target
            for i, schema in enumerate(schemas):
                if str(schema.field('open').type).startswith('fixed_size_binary'):
                    target_schema = schema
                    logger.info(f"Using schema from {parquet_files[i].name} as target")
                    break
            
            # Convert all tables to target schema
            unified_tables = []
            for i, table in enumerate(tables):
                if table.schema.equals(target_schema):
                    unified_tables.append(table)
                else:
                    logger.info(f"Converting schema for {parquet_files[i].name}")
                    # Cast table to target schema
                    unified_table = table.cast(target_schema)
                    unified_tables.append(unified_table)
            tables = unified_tables
            
        # Process new raw data
        logger.info(f"Processing {len(tables)} raw files ({total_raw_rows} rows)...")
        new_data_table = pa.concat_tables(tables)
        
        # CRITICAL FIX: Combine ALL existing consolidated data with new data
        if existing_data_tables:
            logger.info(f"Combining new data with {len(existing_data_tables)} existing consolidated files...")
            
            # Combine all existing consolidated data first
            combined_existing_data = pa.concat_tables(existing_data_tables)
            logger.info(f"Combined existing data: {len(combined_existing_data)} rows")
            
            # Combine existing + new data
            all_tables = [combined_existing_data, new_data_table]
            combined_table = pa.concat_tables(all_tables)
            logger.info(f"Combined total data: {len(combined_table)} rows (existing + new)")
        else:
            # No existing data, just use new raw data
            combined_table = new_data_table
            logger.info(f"No existing data, using only new data: {len(combined_table)} rows")
            
        logger.info(f"Total combined data: {len(combined_table)} rows")
        
        # Sort by timestamp
        logger.info(f"Sorting by {ts_column}...")
        indices = pc.sort_indices(combined_table, sort_keys=[(ts_column, 'ascending')])
        sorted_table = combined_table.take(indices)
        
        # Remove duplicates using pure PyArrow approach (no pandas needed)
        logger.info(f"Removing duplicates (keeping {keep_duplicate})...")
        
        original_count = len(sorted_table)
        
        # Create a dictionary to track seen timestamps and their indices
        seen_timestamps = {}
        
        # Iterate through the sorted table to find duplicates
        for i in range(len(sorted_table)):
            ts_value = sorted_table[ts_column][i].as_py()
            
            if ts_value in seen_timestamps:
                # We've seen this timestamp before
                if keep_duplicate == "first":
                    # We already have the first occurrence, so skip this one
                    continue
                elif keep_duplicate == "last":
                    # Replace the previous index with this one
                    seen_timestamps[ts_value] = i
            else:
                # First time seeing this timestamp
                seen_timestamps[ts_value] = i
        
        # Get the indices to keep
        result_indices = list(seen_timestamps.values())
        
        # Sort the indices to maintain the original order
        result_indices.sort()
        
        # Create the deduplicated table
        deduplicated_table = sorted_table.take(result_indices)
        
        removed_count = len(sorted_table) - len(deduplicated_table)
        logger.info(f"Removed {removed_count} duplicate rows ({removed_count/len(sorted_table)*100:.1f}%)")
        
        # Generate output filename with date range based on final deduplicated data
        min_ts = pc.min(deduplicated_table[ts_column]).as_py()
        max_ts = pc.max(deduplicated_table[ts_column]).as_py()
        
        # Convert nanosecond timestamps to datetime
        from datetime import datetime
        min_date = datetime.fromtimestamp(min_ts / 1e9)
        max_date = datetime.fromtimestamp(max_ts / 1e9)
        
        output_filename = f"consolidated_{min_date.strftime('%Y%m%d')}_to_{max_date.strftime('%Y%m%d')}.parquet"
        output_path = Path(directory) / output_filename
        
        # Write consolidated file using centralized utility
        logger.info(f"Writing consolidated data to {output_path}")

        # Try to extract instrument_id from metadata for Nautilus compatibility
        instrument_id = None
        if deduplicated_table.schema.metadata:
            metadata = dict(deduplicated_table.schema.metadata)
            instrument_id = metadata.get('instrument_id')

        # Use centralized writing function
        from user_scripts_restructured.core.nautilus_data_saver import (
            write_parquet_with_metadata,
            create_nautilus_metadata,
            ParquetWriteConfig
        )

        # Create optimized config for large consolidated files
        config = ParquetWriteConfig(
            compression='snappy',
            use_dictionary=True,
            row_group_size=200000  # Larger row groups for consolidated files
        )

        # Add Nautilus metadata if we can determine instrument_id
        metadata_dict = None
        if instrument_id:
            metadata_dict = create_nautilus_metadata(instrument_id=instrument_id)
            logger.info(f"Adding Nautilus metadata for {instrument_id}")

        success = write_parquet_with_metadata(
            deduplicated_table,
            str(output_path),
            metadata_dict,
            config
        )

        if not success:
            logger.error("Failed to write consolidated file using centralized utility")
            return False
        
        # ATOMIC TRANSACTION: Verify consolidation before committing changes
        # Pass existing consolidated files to verification for complete validation
        existing_files = [path for path, _ in backup_paths] if backup_paths else []
        if verify_consolidation(parquet_files, output_path, ts_column, existing_files):
            logger.info("✓ Consolidation verification successful - committing changes")
            
            # ATOMIC COMMIT: Remove ALL old consolidated files only after verification
            for consolidated_file, backup_path in backup_paths:
                logger.info(f"Removing old consolidated file: {consolidated_file.name}")
                consolidated_file.unlink()
                
                # Clean up backup after successful operation
                logger.info(f"Cleaning up backup: {backup_path.name}")
                backup_path.unlink()
            
            logger.info(f"✓ Successfully removed {len(backup_paths)} old consolidated files")
            
            # PHASE 3 ENHANCEMENT: Three-way file handling (preserve/delete/archive)
            # Priority: archive_mode > remove_originals > preserve_raw (default)
            
            if archive_mode:
                logger.info("Consolidation verified - moving original raw files to archive")
                success = move_files_to_archive(parquet_files, archive_path, directory, dry_run)
                if not success:
                    logger.error("Archive operation failed - keeping files in place")
                    # Continue without failing the entire consolidation
            elif not preserve_raw:
                logger.info("Consolidation verified - removing original raw files")
                for file in parquet_files:
                    logger.info(f"Removing {file.name}")
                    if not dry_run:
                        file.unlink()
            else:
                logger.info("Consolidation verified - preserving original raw files (historical data detected)")

            logger.info(f"✓ Successfully consolidated {len(parquet_files)} raw files into {output_filename}")
            logger.info(f"New raw data: {total_raw_rows} rows → Final consolidated: {len(deduplicated_table)} rows (removed {removed_count} duplicates)")
            return True
        else:
            logger.error("✗ Consolidation verification failed - rolling back changes")
            
            # ATOMIC ROLLBACK: Restore ALL backed-up consolidated files
            rollback_success = True
            if backup_paths:
                logger.info(f"Restoring {len(backup_paths)} backed-up consolidated files...")
                
                # Remove failed consolidated file first
                if output_path.exists():
                    output_path.unlink()
                    logger.info(f"Removed failed consolidated file: {output_path.name}")
                
                # Restore all original consolidated files from backups
                for consolidated_file, backup_path in backup_paths:
                    try:
                        if backup_path.exists():
                            shutil.move(str(backup_path), str(consolidated_file))
                            logger.info(f"✓ Restored: {consolidated_file.name}")
                        else:
                            logger.error(f"✗ Backup not found: {backup_path.name}")
                            rollback_success = False
                    except Exception as e:
                        logger.error(f"✗ Failed to restore {consolidated_file.name}: {e}")
                        rollback_success = False
                
                if rollback_success:
                    logger.info("✓ Successfully restored all backed-up files")
                else:
                    logger.error("✗ Some files could not be restored - manual intervention required")
            
            return False
            
    except Exception as e:
        logger.error(f"✗ Critical error during consolidation: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        
        # EMERGENCY ROLLBACK: Restore ALL backups if any error occurs
        try:
            if backup_paths:
                logger.info(f"Attempting emergency rollback from {len(backup_paths)} backups...")
                
                # Remove any partial consolidated file (if it was created)
                try:
                    if 'output_path' in locals() and output_path and output_path.exists():
                        output_path.unlink()
                        logger.info(f"Removed partial file: {output_path.name}")
                except Exception:
                    # output_path was never assigned or error during cleanup
                    pass
                
                # Restore ALL original consolidated files from backups
                rollback_success = True
                for consolidated_file, backup_path in backup_paths:
                    try:
                        if backup_path.exists():
                            shutil.move(str(backup_path), str(consolidated_file))
                            logger.info(f"✓ Emergency rollback successful: {consolidated_file.name}")
                        else:
                            logger.error(f"✗ Backup not found for emergency rollback: {backup_path.name}")
                            rollback_success = False
                    except Exception as restore_error:
                        logger.error(f"✗ Failed to restore {consolidated_file.name}: {restore_error}")
                        rollback_success = False
                
                if rollback_success:
                    logger.info("✓ Emergency rollback completed successfully")
                else:
                    logger.error("✗ Some emergency rollbacks failed")
                    
            else:
                logger.warning("No backups available for emergency rollback")
                
        except Exception as rollback_error:
            logger.error(f"✗ Emergency rollback failed: {rollback_error}")
            logger.error("CRITICAL: Manual intervention may be required to restore data integrity")
            
        return False


def verify_consolidation(original_files: list, consolidated_file: Path, ts_column: str, existing_files: list = None) -> bool:
    """
    Verify that consolidated file contains all unique data from original (raw) files
    and existing consolidated files.
    
    CRITICAL FIX: Updated to verify that ALL historical data is preserved including
    data from multiple existing consolidated files + new raw data.
    
    Args:
        original_files: List of original (raw) file paths  
        consolidated_file: Path to consolidated file
        ts_column: Timestamp column to verify
        existing_files: List of existing consolidated file paths (optional)
        
    Returns:
        bool: True if verification passes (all data preserved)
    """
    try:
        import pyarrow.parquet as pq
        
        # Read consolidated file
        consolidated_table = pq.read_table(str(consolidated_file))
        consolidated_timestamps = set(consolidated_table[ts_column].to_pylist())
        
        # Collect all unique timestamps from original (raw) files
        original_timestamps = set()
        original_total = 0
        
        for file in original_files:
            table = pq.read_table(str(file))
            original_timestamps.update(table[ts_column].to_pylist())
            original_total += len(table)
            
        # CRITICAL FIX: Also collect timestamps from existing consolidated files
        existing_timestamps = set()
        existing_total = 0
        
        if existing_files:
            logger.info(f"Verifying {len(existing_files)} existing consolidated files are preserved...")
            for file in existing_files:
                table = pq.read_table(str(file))
                existing_timestamps.update(table[ts_column].to_pylist())
                existing_total += len(table)
                logger.info(f"  {file.name}: {len(table)} rows, {len(set(table[ts_column].to_pylist()))} unique timestamps")
        
        # Combined expected timestamps (all original + all existing)
        all_expected_timestamps = original_timestamps | existing_timestamps
        expected_total = original_total + existing_total
        
        # CRITICAL VERIFICATION: Check that ALL data is included in consolidated file
        missing_raw = original_timestamps - consolidated_timestamps
        missing_existing = existing_timestamps - consolidated_timestamps
        
        if missing_raw:
            logger.error(f"Verification failed: {len(missing_raw)} raw timestamps missing from consolidated file")
            return False
            
        if missing_existing:
            logger.error(f"Verification failed: {len(missing_existing)} existing timestamps missing from consolidated file") 
            return False
            
        # Check minimum row count (should be at least as much as unique timestamps)
        if len(consolidated_timestamps) < len(all_expected_timestamps):
            logger.error(f"Consolidated file has fewer unique timestamps than expected: {len(consolidated_timestamps)} < {len(all_expected_timestamps)}")
            return False
            
        # Verification success logging
        logger.info("✓ Verification passed: All historical data preserved in consolidated file")
        logger.info(f"Raw data: {original_total} total rows, {len(original_timestamps)} unique timestamps")
        
        if existing_files:
            logger.info(f"Existing data: {existing_total} total rows, {len(existing_timestamps)} unique timestamps")
            logger.info(f"Combined expected: {expected_total} total rows, {len(all_expected_timestamps)} unique timestamps")
        
        logger.info(f"Consolidated result: {len(consolidated_table)} total rows, {len(consolidated_timestamps)} unique timestamps")
        
        # Calculate duplicates removed
        duplicates_removed = expected_total - len(consolidated_table)
        if duplicates_removed > 0:
            logger.info(f"Duplicates properly removed: {duplicates_removed} duplicate rows")
        
        return True
        
    except Exception as e:
        logger.error(f"Verification error: {e}")
        return False


def validate_inputs(args):
    """Validate input arguments."""
    input_path = Path(args.input)
    if not input_path.exists():
        raise FileNotFoundError(f"Input path does not exist: {args.input}")
    
    # Consolidate mode requires directory input
    if args.consolidate:
        if not input_path.is_dir():
            raise ValueError("Consolidate mode requires a directory input")
        if args.output:
            raise ValueError("Consolidate mode processes files in-place, do not specify output")
    
    if args.output and not args.report_only:
        output_path = Path(args.output)
        if input_path.is_file() and output_path.is_dir():
            raise ValueError("Cannot output file to directory")
        if input_path.is_dir() and output_path.is_file():
            raise ValueError("Cannot output directory to file")
        
        # Create output directory if needed
        if output_path.suffix:  # It's a file
            ensure_directory(output_path.parent)
        else:  # It's a directory
            ensure_directory(output_path)


def load_data_file(file_path: str):
    """Load data from a Nautilus parquet file."""
    try:
        import pyarrow.parquet as pq
        
        # Read the table directly - Nautilus stores data in binary format
        table = pq.read_table(file_path, memory_map=True, pre_buffer=False)
        
        logger.info(f"Loaded table with {len(table)} rows and {len(table.column_names)} columns")
        logger.info(f"Columns: {table.column_names}")
        
        # Get metadata if available
        if table.schema.metadata:
            metadata = table.schema.metadata
            logger.info(f"Table metadata: {dict(metadata)}")
        
        # For Nautilus binary format, we'll work with the raw data
        # Convert to pandas for processing, keeping binary data as-is
        df = table.to_pandas()
        
        # Log data types
        logger.info(f"Data types: {dict(df.dtypes)}")
        
        # Check if we have binary price data (Nautilus format)
        has_binary_prices = any(df[col].dtype == 'object' and 
                               isinstance(df[col].iloc[0], bytes) 
                               for col in ['open', 'high', 'low', 'close'] 
                               if col in df.columns and len(df) > 0)
        
        if has_binary_prices:
            logger.info("Detected Nautilus binary price format - preserving as-is for processing")
        
        # Convert to list of records
        return df.to_dict('records')
        
    except ImportError:
        logger.error("PyArrow is required for data processing")
        return None
    except Exception as e:
        logger.error(f"Error loading file {file_path}: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        return None
    except Exception as e:
        logger.error(f"Error loading file {file_path}: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        return None


def save_data_file(data: list, file_path: str, instrument_id: str = None):
    """
    Save data to a file using centralized nautilus_data_saver utilities.

    Args:
        data: List of bar dictionaries
        file_path: Output file path
        instrument_id: Optional instrument ID for Nautilus metadata

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        from user_scripts_restructured.core.nautilus_data_saver import (
            write_parquet_with_metadata,
            create_nautilus_metadata,
            ParquetWriteConfig
        )
        import pyarrow as pa

        if not data:
            logger.warning(f"No data to save to {file_path}")
            return False

        # Convert list of dicts to PyArrow table
        table = pa.Table.from_pylist(data)

        # Add Nautilus metadata if instrument_id provided
        metadata = None
        if instrument_id:
            metadata = create_nautilus_metadata(instrument_id=instrument_id)
            logger.info(f"Adding Nautilus metadata for {instrument_id}")

        # Use centralized writing function with optimized configuration
        config = ParquetWriteConfig(
            compression='snappy',
            use_dictionary=True,
            row_group_size=100000
        )

        success = write_parquet_with_metadata(table, file_path, metadata, config)

        if success:
            logger.info(f"Successfully saved {len(data)} rows to {file_path}")

        return success

    except ImportError as e:
        logger.error(f"Required modules not available: {e}")
        logger.error("Please ensure nautilus_data_saver module is available")
        return False
    except Exception as e:
        logger.error(f"Error saving file {file_path}: {e}")
        return False


def process_single_file(input_file: str, output_file: str, args, validator: DataValidator, config):
    """Process a single data file."""
    logger.info(f"Processing file: {input_file}")
    
    # Load data
    data = load_data_file(input_file)
    if data is None:
        return False
    
    original_count = len(data)
    logger.info(f"Loaded {original_count:,} bars from {input_file}")
    
    # Create backup if requested
    if args.backup and not args.dry_run:
        backup_path = backup_file(input_file)
        if backup_path:
            logger.info(f"Created backup: {backup_path}")
    
    # Process data
    processed_data = data.copy()
    
    # Remove duplicates
    if args.remove_duplicates:
        duplicate_indices = validator.detect_duplicates(processed_data)
        if duplicate_indices:
            logger.info(f"Found {len(duplicate_indices)} duplicate timestamps")
            if not args.dry_run:
                # Remove duplicates (keep first occurrence)
                processed_data = [bar for i, bar in enumerate(processed_data) if i not in duplicate_indices]
                logger.info(f"Removed {len(duplicate_indices)} duplicates")
        else:
            logger.info("No duplicates found")
    
    # Validate data
    if args.validate:
        valid_data, warnings = validator.validate_bars(processed_data)
        invalid_count = len(processed_data) - len(valid_data)
        if invalid_count > 0:
            logger.warning(f"Found {invalid_count} invalid bars")
            for warning in warnings[:10]:  # Show first 10 warnings
                logger.warning(f"  {warning}")
            if len(warnings) > 10:
                logger.warning(f"  ... and {len(warnings) - 10} more warnings")
            
            if not args.dry_run:
                processed_data = valid_data
                logger.info(f"Removed {invalid_count} invalid bars")
        else:
            logger.info("All bars are valid")
    
    # Handle outliers
    if args.clean_outliers:
        outliers = validator.detect_outliers(processed_data)
        total_outliers = sum(len(indices) for indices in outliers.values())
        
        if total_outliers > 0:
            logger.info(f"Found {total_outliers} outliers")
            for field, indices in outliers.items():
                if indices:
                    logger.info(f"  {field}: {len(indices)} outliers")
            
            if not args.dry_run:
                if args.clean_outliers == "remove":
                    # Remove bars with outliers
                    outlier_bar_indices = set()
                    for indices in outliers.values():
                        outlier_bar_indices.update(indices)
                    processed_data = [bar for i, bar in enumerate(processed_data) if i not in outlier_bar_indices]
                    logger.info(f"Removed {len(outlier_bar_indices)} bars with outliers")
                
                elif args.clean_outliers == "flag":
                    # Add outlier flags to bars
                    for i, bar in enumerate(processed_data):
                        bar['outlier_flags'] = []
                        for field, indices in outliers.items():
                            if i in indices:
                                bar['outlier_flags'].append(f"{field}_outlier")
                    logger.info("Added outlier flags to data")
                
                elif args.clean_outliers == "cap":
                    # Cap outliers at 95th percentile (simplified implementation)
                    logger.info("Outlier capping not fully implemented - flagging instead")
                    for i, bar in enumerate(processed_data):
                        bar['outlier_flags'] = []
                        for field, indices in outliers.items():
                            if i in indices:
                                bar['outlier_flags'].append(f"{field}_outlier")
        else:
            logger.info("No outliers found")
    
    # Generate quality report
    if args.report or args.report_only:
        report = validator.generate_quality_report(processed_data)
        
        if args.output_report:
            try:
                with open(args.output_report, 'w') as f:
                    f.write(report)
                logger.info(f"Quality report saved to: {args.output_report}")
            except Exception as e:
                logger.error(f"Error saving quality report: {e}")
                print(report)  # Fallback to console
        else:
            print("\n" + report)
    
    # Save processed data
    if not args.report_only and not args.dry_run:
        # Try to extract instrument_id from file path or data for Nautilus compatibility
        instrument_id = None
        try:
            # Extract from file path (e.g., MNQ.CME from path)
            file_stem = Path(output_file).stem
            if '.' in file_stem and any(exchange in file_stem for exchange in ['CME', 'NASDAQ', 'NYSE']):
                # Looks like an instrument ID
                instrument_id = file_stem.split('_')[0] if '_' in file_stem else file_stem
                logger.info(f"Extracted instrument_id from filename: {instrument_id}")
        except:
            pass

        if save_data_file(processed_data, output_file, instrument_id):
            final_count = len(processed_data)
            reduction = original_count - final_count
            logger.info(f"Saved {final_count:,} bars to {output_file}")
            if reduction > 0:
                logger.info(f"Removed {reduction:,} bars ({reduction/original_count*100:.1f}% reduction)")
        else:
            return False
    
    # Auto-rename file if enabled and processing was successful
    should_auto_rename = (
        (args.auto_rename or 
         (config.file_management.auto_rename_after_processing and not args.no_auto_rename)) and
        not args.report_only and
        not args.dry_run
    )
    
    if should_auto_rename:
        try:
            target_file = output_file if output_file else input_file
            file_manager = DataFileManager(
                backup_enabled=config.file_management.backup_before_rename,
                dry_run=False
            )
            
            rename_result = file_manager.rename_file_by_content(target_file)
            
            if rename_result.success:
                if rename_result.renamed:
                    logger.info(f"Auto-renamed: {rename_result}")
                    logger.info("Date-range filename provides better reference for data tracking")
                else:
                    logger.debug(f"File naming already correct: {Path(target_file).name}")
            else:
                logger.warning(f"Auto-rename failed: {rename_result}")
                
        except Exception as e:
            logger.error(f"Error during auto-rename: {e}")
            # Don't fail the entire process for rename errors
    
    return True


def process_directory(input_dir: str, output_dir: str, args, validator: DataValidator, config):
    """Process all files in a directory."""
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # Find all parquet files
    parquet_files = find_files(input_path, "*.parquet", recursive=True)
    
    if not parquet_files:
        logger.warning(f"No parquet files found in {input_dir}")
        return False
    
    logger.info(f"Found {len(parquet_files)} parquet files to process")
    
    success_count = 0
    
    for input_file in parquet_files:
        # Calculate relative path and output file
        rel_path = input_file.relative_to(input_path)
        output_file = output_path / rel_path
        
        # Ensure output directory exists
        ensure_directory(output_file.parent)
        
        try:
            if process_single_file(str(input_file), str(output_file), args, validator, config):
                success_count += 1
            else:
                logger.error(f"Failed to process {input_file}")
        except Exception as e:
            logger.error(f"Error processing {input_file}: {e}")
    
    logger.info(f"Successfully processed {success_count}/{len(parquet_files)} files")
    return success_count == len(parquet_files)


def main():
    """Main function."""
    args = parse_args()
    
    # Setup logging
    if args.log_file:
        setup_logging(level=args.log_level, log_file=args.log_file)
    else:
        setup_logging(level=args.log_level, console=True)
    
    logger.info("=" * 60)
    logger.info("DATA PROCESSING")
    logger.info("=" * 60)
    logger.info(f"Input: {args.input}")
    logger.info(f"Output: {args.output}")
    logger.info(f"Remove duplicates: {args.remove_duplicates}")
    logger.info(f"Consolidate: {args.consolidate}")
    logger.info(f"Validate: {args.validate}")
    logger.info(f"Clean outliers: {args.clean_outliers}")
    logger.info(f"Generate report: {args.report or args.report_only}")
    logger.info(f"Dry run: {args.dry_run}")
    
    try:
        # Validate inputs
        validate_inputs(args)
        
        # Load configuration
        config = ConfigManager.load_config(args.config)
        logger.info("Configuration loaded successfully")
        
        # Create data validator
        validator = DataValidator()
        
        # Check if PyArrow is available
        try:
            import pyarrow
            logger.info(f"PyArrow version: {pyarrow.__version__}")
        except ImportError:
            logger.error("PyArrow is required for data processing")
            logger.info("Install with: pip install pyarrow")
            return 1
        
        # Process data
        input_path = Path(args.input)
        start_time = datetime.now()
        
        # Handle consolidation mode
        if args.consolidate:
            logger.info("Starting file consolidation...")
            ts_column = 'ts_event'  # Use Nautilus standard timestamp column
            
            # PHASE 3 ENHANCEMENT: Handle archive mode parameters
            archive_mode = getattr(args, 'archive_mode', False)
            archive_path = getattr(args, 'archive_path', None)
            
            # Determine preserve_raw: if --remove-originals is set, preserve_raw=False
            preserve_raw = not args.remove_originals if hasattr(args, 'remove_originals') else True
            
            # Archive mode takes precedence over preserve_raw
            if archive_mode:
                logger.info("✓ Archive mode enabled - files will be moved to archive after consolidation")
                
            success = consolidate_files(
                str(input_path), 
                ts_column=ts_column, 
                dry_run=args.dry_run, 
                preserve_raw=preserve_raw,
                archive_mode=archive_mode,
                archive_path=archive_path
            )
        elif input_path.is_file():
            # Process single file
            if args.report_only:
                # Just generate report
                success = process_single_file(str(input_path), "", args, validator, config)
            else:
                if not args.output:
                    logger.error("Output file required when processing single file")
                    return 1
                success = process_single_file(str(input_path), args.output, args, validator, config)
        else:
            # Process directory
            if args.report_only:
                logger.error("Report-only mode not supported for directories")
                return 1
            if not args.output:
                logger.error("Output directory required when processing directory")
                return 1
            success = process_directory(str(input_path), args.output, args, validator, config)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if success:
            logger.info("✓ Data processing completed successfully")
            logger.info(f"Processing time: {duration:.1f} seconds")
            return 0
        else:
            logger.error("✗ Data processing failed")
            return 1
            
    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Error: {e}")
        import traceback
        logger.debug(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main())
