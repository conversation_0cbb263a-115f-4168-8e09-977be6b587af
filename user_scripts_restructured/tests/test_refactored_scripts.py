#!/usr/bin/env python3
"""
Integration tests for refactored scripts.

This test suite validates that all refactored scripts maintain their
existing functionality while using the centralized parquet writing system.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
import pyarrow as pa
import pyarrow.parquet as pq
import sys
import os

# Add the project root to the path
project_root = Path(__file__).resolve().parents[2]
sys.path.insert(0, str(project_root))


class TestRefactoredScripts:
    """Test suite for refactored scripts."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for tests."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def sample_bar_data(self):
        """Create sample bar data for testing."""
        return [
            {
                'open': 15000.0,
                'high': 15010.0,
                'low': 14995.0,
                'close': 15005.0,
                'volume': 100,
                'ts_init': 1640995200000000000,
                'ts_event': 1640995200000000000
            },
            {
                'open': 15005.0,
                'high': 15015.0,
                'low': 15000.0,
                'close': 15010.0,
                'volume': 150,
                'ts_init': 1640995260000000000,
                'ts_event': 1640995260000000000
            }
        ]
    
    def test_process_data_save_function(self, temp_dir, sample_bar_data):
        """Test the refactored save_data_file function in process_data.py."""
        from user_scripts_restructured.scripts.process_data import save_data_file
        
        output_file = Path(temp_dir) / "test_process_data.parquet"
        
        # Test basic saving
        success = save_data_file(sample_bar_data, str(output_file))
        assert success is True
        assert output_file.exists()
        
        # Verify file can be read
        table = pq.read_table(str(output_file))
        assert len(table) == 2
        
        # Test with instrument_id
        output_file2 = Path(temp_dir) / "test_with_instrument.parquet"
        success = save_data_file(sample_bar_data, str(output_file2), "MNQ.CME")
        assert success is True
        
        # Verify Nautilus metadata was added
        table2 = pq.read_table(str(output_file2))
        if table2.schema.metadata:
            metadata = dict(table2.schema.metadata)
            assert 'instrument_id' in metadata
    
    def test_consolidator_processor(self, temp_dir, sample_bar_data):
        """Test the refactored consolidator processor."""
        from user_scripts_restructured.processors.consolidator import DataConsolidator
        
        # Create input files
        input_files = []
        for i in range(2):
            file_path = Path(temp_dir) / f"input_{i}.parquet"
            table = pa.Table.from_pylist(sample_bar_data)
            pq.write_table(table, str(file_path))
            input_files.append(str(file_path))
        
        output_file = Path(temp_dir) / "consolidated.parquet"
        
        # Test consolidation
        consolidator = DataConsolidator()
        result = consolidator.consolidate_files(
            input_files=input_files,
            output_file=str(output_file),
            remove_duplicates=True,
            validate_data=False,  # Skip validation for speed
            sort_by_timestamp=True
        )
        
        assert result.success is True
        assert output_file.exists()
        assert result.total_output_rows > 0
    
    def test_base_downloader_fallback(self, temp_dir, sample_bar_data):
        """Test the refactored fallback method in base_downloader.py."""
        from user_scripts_restructured.downloaders.base_downloader import BaseDownloader
        from user_scripts_restructured.core.nautilus_data_saver import SaveResult
        
        # Create a mock downloader
        downloader = BaseDownloader(use_nautilus_saver=False)
        
        # Test fallback saving
        result = downloader._save_bars_fallback(sample_bar_data, "MNQ")
        
        assert isinstance(result, SaveResult)
        assert result.success is True
        assert result.bars_saved == 2
        assert Path(result.file_path).exists()
    
    def test_continuous_futures_processor_integration(self, temp_dir, sample_bar_data):
        """Test that continuous futures processor uses centralized functions."""
        # This is a simplified test since the full processor is complex
        # We'll test that the utility functions are available and work
        
        from user_scripts_restructured.core.nautilus_data_saver import (
            save_table_with_nautilus_metadata,
            ParquetWriteConfig
        )
        
        # Create a test table
        table = pa.Table.from_pylist(sample_bar_data)
        output_file = Path(temp_dir) / "continuous_test.parquet"
        
        # Test the function used by continuous futures processor
        config = ParquetWriteConfig(
            compression='snappy',
            use_dictionary=True,
            row_group_size=200000
        )
        
        success = save_table_with_nautilus_metadata(
            table=table,
            file_path=str(output_file),
            instrument_id="MNQCONT.CME",
            bar_type="1-MINUTE-LAST-EXTERNAL",
            price_precision=2,
            size_precision=0,
            config=config
        )
        
        assert success is True
        assert output_file.exists()
        
        # Verify metadata
        written_table = pq.read_table(str(output_file))
        metadata = dict(written_table.schema.metadata)
        assert metadata['instrument_id'] == "MNQCONT.CME"
    
    def test_create_continuous_futures_script_validation(self, temp_dir, sample_bar_data):
        """Test validation functions used in create_continuous_futures.py."""
        from user_scripts_restructured.core.nautilus_data_saver import validate_parquet_compatibility
        
        # Create a test table
        table = pa.Table.from_pylist(sample_bar_data)
        
        # Test validation (used in the refactored script)
        is_valid, issues = validate_parquet_compatibility(table, for_nautilus=True)
        assert is_valid is True
        assert len(issues) == 0
    
    def test_metadata_consistency_across_components(self, temp_dir, sample_bar_data):
        """Test that all components create consistent Nautilus metadata."""
        from user_scripts_restructured.core.nautilus_data_saver import (
            create_nautilus_metadata,
            save_table_with_nautilus_metadata
        )
        
        # Test metadata creation consistency
        metadata1 = create_nautilus_metadata("MNQ.CME", "1-MINUTE-LAST-EXTERNAL", 2, 0)
        metadata2 = create_nautilus_metadata("MNQ.CME", "1-MINUTE-LAST-EXTERNAL", 2, 0)
        
        assert metadata1 == metadata2
        assert metadata1['bar_type'] == "MNQ.CME-1-MINUTE-LAST-EXTERNAL"
        
        # Test that saved files have consistent metadata
        table = pa.Table.from_pylist(sample_bar_data)
        
        files = []
        for i in range(3):
            file_path = Path(temp_dir) / f"metadata_test_{i}.parquet"
            success = save_table_with_nautilus_metadata(
                table=table,
                file_path=str(file_path),
                instrument_id="MNQ.CME",
                bar_type="1-MINUTE-LAST-EXTERNAL",
                price_precision=2,
                size_precision=0
            )
            assert success is True
            files.append(file_path)
        
        # Verify all files have identical metadata
        reference_metadata = None
        for file_path in files:
            table = pq.read_table(str(file_path))
            metadata = dict(table.schema.metadata)
            
            if reference_metadata is None:
                reference_metadata = metadata
            else:
                assert metadata == reference_metadata
    
    def test_error_handling_consistency(self, temp_dir):
        """Test that error handling is consistent across components."""
        from user_scripts_restructured.core.nautilus_data_saver import (
            write_parquet_with_metadata,
            create_fallback_parquet_file
        )
        
        # Test with empty data
        empty_table = pa.Table.from_pylist([])
        invalid_path = "/invalid/path/file.parquet"
        
        # Should handle errors gracefully
        success = write_parquet_with_metadata(empty_table, invalid_path)
        assert success is False
        
        # Test fallback with empty data
        result = create_fallback_parquet_file([], "TEST", temp_dir)
        assert result.success is False
        assert result.error_message != ""
    
    def test_configuration_consistency(self):
        """Test that configuration objects work consistently."""
        from user_scripts_restructured.core.nautilus_data_saver import (
            ParquetWriteConfig,
            get_optimal_compression_settings
        )
        
        # Test default configuration
        default_config = ParquetWriteConfig()
        assert default_config.compression == 'snappy'
        assert default_config.use_dictionary is True
        
        # Test optimal settings
        small_config = get_optimal_compression_settings(5.0)
        large_config = get_optimal_compression_settings(150.0)
        
        # Should have different settings for different file sizes
        assert small_config.row_group_size != large_config.row_group_size
        assert small_config.compression != large_config.compression


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
